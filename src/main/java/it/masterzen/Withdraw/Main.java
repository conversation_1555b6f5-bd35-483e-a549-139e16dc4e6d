package it.masterzen.Withdraw;

import com.sun.corba.se.spi.extension.ServantCachingPolicy;
import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import io.netty.util.internal.shaded.org.jctools.queues.SpscLinkedQueue;
import it.masterzen.Keys.GUI;
import it.masterzen.Keys.KeyList;
import it.masterzen.Keys.KeyManager;
import it.masterzen.Resume.Resume;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import it.masterzen.blockbreak.XPManager;
import it.masterzen.commands.PointShop;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.RegisteredServiceProvider;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private final AlphaBlockBreak mainClass;
    private final TokenEnchantAPI teAPI;
    private Economy economy;
    private Resume resume;

    public final String prefix = "§e§lWITHDRAW §8»§7 ";

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        teAPI = TokenEnchantAPI.getInstance();
        RegisteredServiceProvider<Economy> rsp = mainClass.getServer().getServicesManager().getRegistration(Economy.class);
        economy = rsp.getProvider();

        resume = mainClass.getResume();
    }

    @EventHandler
    public void onPlayerClicks(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        Action action = event.getAction();

        if (action.equals(Action.RIGHT_CLICK_AIR) || action.equals(Action.RIGHT_CLICK_BLOCK)) {
            ItemStack itemInHand = player.getInventory().getItemInMainHand();

            if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasDisplayName() && itemInHand.getItemMeta().hasLore()) {
                if (itemInHand.getItemMeta().getDisplayName().equalsIgnoreCase("§a§lMoney Note §7(Right Click)") ||
                    itemInHand.getItemMeta().getDisplayName().equalsIgnoreCase("§a§lTokens Note §7(Right Click)") ||
                    itemInHand.getItemMeta().getDisplayName().equalsIgnoreCase("§a§lXP Bottle §7(Right Click)") ||
                    itemInHand.getItemMeta().getDisplayName().equalsIgnoreCase("§b§lBeacon Note §7(Right Click)")) {

                    event.setCancelled(true);
                    List<String> lore = itemInHand.getItemMeta().getLore();
                    if (lore.get(1).contains("§dValue:")) {
                        String value = lore.get(1).replace("§dValue: §f", "");
                        double amount = 0;
                        //"","k", "M", "B", "T", "Q", "Qt", "Sx", "Sp", "Ot", "Nn"

                        if (value.contains("Sp")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("Sp", "")) * 1000000000000000000000000D;
                        } else if (value.contains("Sx")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("Sx", "")) * 1000000000000000000000D;
                        } else if (value.contains("Qt")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("Qt", "")) * 1000000000000000000D;
                        } else if (value.contains("Q")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("Q", "")) * 1000000000000000D;
                        } else if (value.contains("T")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("T", "")) * 1000000000000D;
                        } else if (value.contains("B")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("B", "")) * 1000000000D;
                        } else if (value.contains("M")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("M", "")) * 1000000D;
                        } else if (value.contains("k")) {
                            amount = Double.parseDouble(ChatColor.stripColor(value).replace("k", "")) * 1000D;
                        } else {
                            amount = Double.parseDouble(ChatColor.stripColor(value));
                        }

                        if (lore.get(0).contains("Tokens")) {
                            teAPI.addTokens(player, amount);
                            itemInHand.setAmount(itemInHand.getAmount() - 1);
                            player.sendMessage(prefix + "You claimed §a§l" + value + " §7Token(s)");
                        } else if (lore.get(0).contains("Money")) {
                            economy.depositPlayer(player, amount);
                            itemInHand.setAmount(itemInHand.getAmount() - 1);
                            player.sendMessage(prefix + "You claimed §a§l" + value + " §7Money");
                        } else if (lore.get(0).contains("XP")) {
                            if (XPManager.getTotalExperience(player) + amount < Integer.MAX_VALUE) {
                                XPManager.setTotalExperience(player, (XPManager.getTotalExperience(player) + (int) amount));
                                itemInHand.setAmount(itemInHand.getAmount() - 1);
                                player.sendMessage(prefix + "You claimed §a§l" + value + " §7XP");
                            } else {
                                event.setCancelled(true);
                                player.sendMessage(prefix + "§cYou can't claim this bottle right now");
                                player.sendMessage("§7§o(in Minecraft there's a limit of 2b XP)");
                            }
                        } else if (lore.get(0).contains("Beacon")) {
                            //XPManager.setTotalExperience(player, (XPManager.getTotalExperience(player) + (int) amount));
                            int freeSlots = mainClass.getEmptySlots(player.getInventory());
                            if (mainClass.getBeaconBackpack().hasBackpack(player) || freeSlots >= (amount /64 )) {
                                mainClass.getBeaconBackpack().addBeacons(player, (long) amount);
                                itemInHand.setAmount(itemInHand.getAmount() - 1);
                                player.sendMessage(prefix + "You claimed §a§l" + value + " §7Beacon(s)");
                            } else {
                                player.sendMessage(prefix + "§cYou don't have enough space in your inventory");
                            }
                        } else {
                            player.sendMessage(prefix + "§cCan't claim this item. Report this on /discord");
                        }
                    }
                }
            }
        }
    }

    public void giveItem(Player player, String currency, double amount) {
        int successfulWithdraw = 0;
        int unsuccessfulWithdraw = 0;

        if (mainClass.getEmptySlots(player.getInventory()) <= 0) {
            player.sendMessage(prefix + "§cInventory Full !");
        } else {
            if (currency.equalsIgnoreCase("Money")) {
                ItemStack item = new ItemStack(Material.PAPER);
                ItemMeta itemMeta = item.getItemMeta();

                itemMeta.setDisplayName("§a§lMoney Note §7(Right Click)");
                List<String> Lore = new ArrayList<>();
                Lore.add("§dType: §fMoney");
                Lore.add("§dValue: §f" + mainClass.newFormatNumber(amount, player));
                itemMeta.setLore(Lore);
                item.setItemMeta(itemMeta);

                player.getInventory().addItem(item);
            } else if (currency.equalsIgnoreCase("Tokens") || currency.equalsIgnoreCase("Token")) {
                ItemStack item = new ItemStack(Material.PAPER);
                ItemMeta itemMeta = item.getItemMeta();

                itemMeta.setDisplayName("§a§lTokens Note §7(Right Click)");
                List<String> Lore = new ArrayList<>();
                Lore.add("§dType: §fTokens");
                Lore.add("§dValue: §f" + mainClass.newFormatNumber(amount, player));
                itemMeta.setLore(Lore);
                item.setItemMeta(itemMeta);

                player.getInventory().addItem(item);
            } else if (currency.equalsIgnoreCase("XP")) {
                ItemStack item = new ItemStack(Material.EXP_BOTTLE);
                ItemMeta itemMeta = item.getItemMeta();

                if (amount <= 1000000000) {
                    itemMeta.setDisplayName("§a§lXP Bottle §7(Right Click)");
                    List<String> Lore = new ArrayList<>();
                    Lore.add("§dType: §7XP");
                    Lore.add("§dValue: §f" + mainClass.newFormatNumber(amount, player));
                    itemMeta.setLore(Lore);
                    item.setItemMeta(itemMeta);
                    player.getInventory().addItem(item);
                } else {
                    int maxIteraions = 1000;
                    while (amount > 0 && maxIteraions > 0) {
                        if (amount > 1000000000) {
                            itemMeta.setDisplayName("§a§lXP Bottle §7(Right Click)");
                            List<String> Lore = new ArrayList<>();
                            Lore.add("§dType: §7XP");
                            Lore.add("§dValue: §f1B");
                            itemMeta.setLore(Lore);
                            item.setItemMeta(itemMeta);
                            amount = amount - 1000000000;
                            player.getInventory().addItem(item);
                        } else {
                            itemMeta.setDisplayName("§a§lXP Bottle §7(Right Click)");
                            List<String> Lore = new ArrayList<>();
                            Lore.add("§dType: §7XP");
                            Lore.add("§dValue: §f" + mainClass.newFormatNumber(amount, player));
                            itemMeta.setLore(Lore);
                            item.setItemMeta(itemMeta);
                            amount = 0;
                            player.getInventory().addItem(item);
                        }
                        maxIteraions--;
                    }
                }
            }
        }
    }

    public void withdraw(Player player, String currency, double amount, int times, boolean max, boolean sendMessage) {
        //boolean messageSent = false;
        //boolean messageErrorSent = false;
        int successfulWithdraw = 0;
        int unsuccessfulWithdraw = 0;

        for (int i = 0; i < times; i++) {
            if (mainClass.getEmptySlots(player.getInventory()) <= 0) {
                player.sendMessage(prefix + "§cInventory Full !");
            } else {
                if (currency.equalsIgnoreCase("Money")) {
                    if (max) {
                        amount = Math.floor(economy.getBalance(player));
                    }
                    if (amount > 0 && economy.has(player, amount)) {
                        economy.withdrawPlayer(player, amount);

                        ItemStack item = new ItemStack(Material.PAPER);
                        ItemMeta itemMeta = item.getItemMeta();

                        itemMeta.setDisplayName("§a§lMoney Note §7(Right Click)");
                        List<String> Lore = new ArrayList<>();
                        Lore.add("§dType: §fMoney");
                        //Lore.add("§dValue: §f" + AlphaBlockBreak.newFormatNumber(amount));
                        Lore.add("§dValue: §f" + amount);
                        itemMeta.setLore(Lore);
                        item.setItemMeta(itemMeta);

                        player.getInventory().addItem(item);
                        successfulWithdraw++;
                        //if (!messageSent) {
                        //    player.sendMessage(prefix + "§aYou successfully withdraw §a" + AlphaBlockBreak.newFormatNumber(amount) + " Money " + (times > 1 ? "§7§o(" + (i + 1) + "x)" : ""));
                        //    messageSent = true;
                        //}
                    } else {
                        unsuccessfulWithdraw++;
                        //if (!messageErrorSent) {
                        //    player.sendMessage(prefix + "§cYou don't have enough Money");
                        //    messageErrorSent = true;
                        //}
                    }
                } else if (currency.equalsIgnoreCase("Tokens") || currency.equalsIgnoreCase("Token")) {
                    if (max) {
                        amount = teAPI.getTokens(player);
                    }
                    if (amount > 0 && teAPI.getTokens(player) >= amount) {
                        teAPI.removeTokens(player, amount);

                        ItemStack item = new ItemStack(Material.PAPER);
                        ItemMeta itemMeta = item.getItemMeta();

                        itemMeta.setDisplayName("§a§lTokens Note §7(Right Click)");
                        List<String> Lore = new ArrayList<>();
                        Lore.add("§dType: §fTokens");
                        //Lore.add("§dValue: §f" + AlphaBlockBreak.newFormatNumber(amount));
                        Lore.add("§dValue: §f" + amount);
                        itemMeta.setLore(Lore);
                        item.setItemMeta(itemMeta);

                        player.getInventory().addItem(item);
                        successfulWithdraw++;
                        //if (!messageSent) {
                        //    player.sendMessage(prefix + "§aYou successfully withdraw §a" + AlphaBlockBreak.newFormatNumber(amount) + " Tokens " + (times > 1 ? "§7§o(" + (i + 1) + "x)" : ""));
                        //    messageSent = true;
                        //}
                    } else {
                        unsuccessfulWithdraw++;
                        //if (!messageErrorSent) {
                        //    player.sendMessage(prefix + "§cYou don't have enough Tokens");
                        //    messageErrorSent = true;
                        //}
                    }
                } else if (currency.equalsIgnoreCase("XP")) {
                    int totalXP = XPManager.getTotalExperience(player);
                    if (max) {
                        amount = totalXP;
                    }
                    if (amount > 0 && totalXP >= amount) {
                        XPManager.setTotalExperience(player, totalXP - (int) amount);

                        ItemStack item = new ItemStack(Material.EXP_BOTTLE);
                        ItemMeta itemMeta = item.getItemMeta();

                        itemMeta.setDisplayName("§a§lXP Bottle §7(Right Click)");
                        List<String> Lore = new ArrayList<>();
                        Lore.add("§dType: §7XP");
                        //Lore.add("§dValue: §f" + AlphaBlockBreak.newFormatNumber(amount));
                        Lore.add("§dValue: §f" + amount);
                        itemMeta.setLore(Lore);
                        item.setItemMeta(itemMeta);

                        player.getInventory().addItem(item);
                        successfulWithdraw++;
                        //if (!messageSent) {
                        //    player.sendMessage(prefix + "§aYou successfully withdraw §a" + AlphaBlockBreak.newFormatNumber(amount) + " XP " + (times > 1 ? "§7§o(" + (i + 1) + "x)" : ""));
                        //    messageSent = true;
                        //}
                    } else {
                        unsuccessfulWithdraw++;
                        //if (!messageErrorSent) {
                        //    player.sendMessage(prefix + "§cYou don't have enough XP");
                        //    messageErrorSent = true;
                        //}
                    }
                } else if (currency.equalsIgnoreCase("Beacon") || currency.equalsIgnoreCase("Beacons")) {
                    if (mainClass.getBeaconBackpack().hasBackpack(player)) {
                        if (mainClass.getBeaconBackpack().isBackpack(player.getInventory().getItemInMainHand())) {
                            ItemStack itemInHand = player.getInventory().getItemInMainHand();
                            if (max) {
                                amount = mainClass.getBeaconBackpack().getActualPoints(itemInHand);
                            }
                            if (amount > 0 && mainClass.getBeaconBackpack().getActualPoints(itemInHand) >= amount) {
                                mainClass.getBeaconBackpack().removeBeacons(player, (long) amount);
                                successfulWithdraw++;
                            } else {
                                unsuccessfulWithdraw++;
                            }
                        } else {
                            player.sendMessage(prefix + "Please hold the Backpack to withdraw beacons");
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou must have a Beacon Backpack to use this command");
                    }
                } else {
                    player.sendMessage("§c" + currency + " is not a valid currency");
                    sendCorrectUsage(player);
                }
            }
        }

        if (sendMessage) {
            if ((currency.equalsIgnoreCase("Tokens") || currency.equalsIgnoreCase("Token")) && successfulWithdraw > 0) {
                player.sendMessage(prefix + "§aYou successfully withdraw §a" + mainClass.newFormatNumber(amount, player) + " Tokens " + (successfulWithdraw > 1 ? "§7§o(" + successfulWithdraw + "x)" : ""));
            } else if (currency.equalsIgnoreCase("Money") && successfulWithdraw > 0) {
                player.sendMessage(prefix + "§aYou successfully withdraw §a" + mainClass.newFormatNumber(amount, player) + " Money " + (successfulWithdraw > 1 ? "§7§o(" + successfulWithdraw + "x)" : ""));
            } else if (currency.equalsIgnoreCase("XP") && successfulWithdraw > 0) {
                player.sendMessage(prefix + "§aYou successfully withdraw §a" + mainClass.newFormatNumber(amount, player) + " XP " + (successfulWithdraw > 1 ? "§7§o(" + successfulWithdraw + "x)" : ""));
            } else if ((currency.equalsIgnoreCase("Beacons") || currency.equalsIgnoreCase("Beacon")) && successfulWithdraw > 0) {
                player.sendMessage(prefix + "§aYou successfully withdraw §a" + mainClass.newFormatNumber(amount, player) + " Beacons " + (successfulWithdraw > 1 ? "§7§o(" + successfulWithdraw + "x)" : ""));
            }
            if (unsuccessfulWithdraw > 0) {
                player.sendMessage(prefix + "§cUnsuccessful Withdraw §7§o(" + unsuccessfulWithdraw + "x)");
            }
        }
    }

    public boolean isInt(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public boolean isDouble(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public void sendCorrectUsage(Player player) {
        player.sendMessage(prefix + "Correct usage: /withdraw [currency] [amount] (times)");
        player.sendMessage("§7Currency list: Money, Tokens, XP, Beacon §o(only for backpack)");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("withdraw")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.isOp() && args.length > 0 && args[0].equalsIgnoreCase("give")) {
                    giveItem(Bukkit.getPlayer(args[1]), args[2], Double.parseDouble(args[3]));
                } else if (args.length >= 2 && args.length <= 3) {
                    if (args.length == 2) {
                        if (isDouble(args[1]) && Double.parseDouble(args[1]) > 0) {
                            withdraw(player, args[0], Double.parseDouble(args[1]), 1, false, true);
                        }
                    } else {
                        if (isDouble(args[1]) && isDouble(args[2]) && Double.parseDouble(args[1]) > 0) {
                            if (Integer.parseInt(args[2]) <= 512) {
                                withdraw(player, args[0], Double.parseDouble(args[1]), Integer.parseInt(args[2]), false, true);
                            } else {
                                player.sendMessage(prefix + "§cYou can use a max of §l512 §ctimes");
                            }
                        }
                    }
                } else {
                    sendCorrectUsage(player);
                }
            } else {
                giveItem(Bukkit.getPlayer(args[1]), args[2], Double.parseDouble(args[3]));
            }
        } else if (cmd.getName().equalsIgnoreCase("withdrawall")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 1) {
                    withdraw(player, args[0], 0, 1, true, true);
                }
            }
        }
        return false;
    }
}
