package it.masterzen.minebuddy;

import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * In-memory tracker for player activity data used by the AFK detection system.
 * This class stores real-time activity information that doesn't need to be persisted.
 */
public class PlayerActivityTracker {
    
    private final UUID playerId;
    private final String playerName;
    
    // Location tracking
    private Location lastKnownLocation;
    private Location previousLocation;
    private long lastMovementTime;
    private double totalMovementDistance;
    private int stationaryChecks;
    
    // Look direction tracking
    private float lastYaw;
    private float lastPitch;
    private long lastLookChangeTime;
    private int lookChangeCount;
    
    // Interaction tracking
    private long lastInteractionTime;
    private int interactionCount;
    private long lastChatTime;
    private int chatCount;
    
    // Activity scoring
    private double currentActivityScore;
    private long lastActivityUpdate;
    private List<ActivityEvent> recentActivities;
    
    // Pattern detection
    private List<Location> movementHistory;
    private List<Float> yawHistory;
    private boolean suspiciousPatternDetected;
    
    // Warnings and penalties
    private int warningCount;
    private long lastWarningTime;
    private double penaltyMultiplier;
    
    public PlayerActivityTracker(UUID playerId, String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.lastMovementTime = System.currentTimeMillis();
        this.lastLookChangeTime = System.currentTimeMillis();
        this.lastInteractionTime = System.currentTimeMillis();
        this.lastActivityUpdate = System.currentTimeMillis();
        this.currentActivityScore = 100.0; // Start with full activity score
        this.penaltyMultiplier = 1.0; // No penalty initially
        this.recentActivities = new ArrayList<>();
        this.movementHistory = new ArrayList<>();
        this.yawHistory = new ArrayList<>();
    }
    
    /**
     * Updates location data and calculates movement
     */
    public void updateLocation(Location newLocation) {
        if (lastKnownLocation != null) {
            // Check if locations are in the same world
            if (lastKnownLocation.getWorld().equals(newLocation.getWorld())) {
                // Calculate movement distance
                double distance = lastKnownLocation.distance(newLocation);

                if (distance > 0.1) { // Minimum movement threshold to avoid micro-movements
                    previousLocation = lastKnownLocation.clone();
                    totalMovementDistance += distance;
                    lastMovementTime = System.currentTimeMillis();
                    stationaryChecks = 0;

                    // Add to movement history for pattern detection
                    addToMovementHistory(newLocation);

                    // Award activity points for movement
                    addActivityScore(Math.min(distance * 2.0, 10.0)); // Cap at 10 points per movement
                } else {
                    stationaryChecks++;
                }
            }
        }

        lastKnownLocation = newLocation.clone();
    }
    
    /**
     * Updates look direction data
     */
    public void updateLookDirection(float yaw, float pitch) {
        if (lastYaw != 0 || lastPitch != 0) {
            float yawDiff = Math.abs(yaw - lastYaw);
            float pitchDiff = Math.abs(pitch - lastPitch);
            
            // Normalize yaw difference (handle 360° wrap-around)
            if (yawDiff > 180) {
                yawDiff = 360 - yawDiff;
            }
            
            // Check for meaningful look changes
            if (yawDiff > 5.0 || pitchDiff > 5.0) {
                lastLookChangeTime = System.currentTimeMillis();
                lookChangeCount++;
                
                // Add to yaw history for pattern detection
                addToYawHistory(yaw);
                
                // Award activity points for look changes
                addActivityScore(Math.min((yawDiff + pitchDiff) * 0.5, 5.0)); // Cap at 5 points
            }
        }
        
        lastYaw = yaw;
        lastPitch = pitch;
    }
    
    /**
     * Records player interaction activity
     */
    public void recordInteraction(InteractionType type) {
        lastInteractionTime = System.currentTimeMillis();
        interactionCount++;
        
        // Award activity points based on interaction type
        double points = getInteractionPoints(type);
        addActivityScore(points);
        
        // Record the activity event
        recentActivities.add(new ActivityEvent(type, System.currentTimeMillis()));
        
        // Keep only recent activities (last 5 minutes)
        cleanupOldActivities();
    }
    
    /**
     * Records chat activity
     */
    public void recordChat() {
        lastChatTime = System.currentTimeMillis();
        chatCount++;
        addActivityScore(15.0); // High points for chat activity
        
        recentActivities.add(new ActivityEvent(InteractionType.CHAT, System.currentTimeMillis()));
        cleanupOldActivities();
    }
    
    /**
     * Decays activity score over time
     */
    public void decayActivityScore() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastUpdate = currentTime - lastActivityUpdate;
        
        if (timeSinceLastUpdate > 30000) { // Decay every 30 seconds
            double decayAmount = (timeSinceLastUpdate / 30000.0) * 2.0; // 2 points per 30 seconds
            currentActivityScore = Math.max(0.0, currentActivityScore - decayAmount);
            lastActivityUpdate = currentTime;
        }
    }
    
    /**
     * Checks if player appears to be AFK based on various criteria
     */
    public boolean isPlayerAFK(AFKDetectionConfig config) {
        long currentTime = System.currentTimeMillis();
        
        // Check time-based criteria
        boolean noRecentMovement = (currentTime - lastMovementTime) > config.getMaxStationaryTime();
        boolean noRecentLookChange = (currentTime - lastLookChangeTime) > config.getMaxStationaryTime();
        boolean noRecentInteraction = (currentTime - lastInteractionTime) > config.getMaxInactivityTime();
        
        // Check activity score
        boolean lowActivityScore = currentActivityScore < config.getMinActivityScore();
        
        // Check stationary checks
        boolean tooManyStationaryChecks = stationaryChecks > config.getMaxStationaryChecks();
        
        // Combine criteria (require multiple indicators for AFK detection)
        int afkIndicators = 0;
        if (noRecentMovement) afkIndicators++;
        if (noRecentLookChange) afkIndicators++;
        if (noRecentInteraction) afkIndicators++;
        if (lowActivityScore) afkIndicators++;
        if (tooManyStationaryChecks) afkIndicators++;
        
        return afkIndicators >= config.getMinAfkIndicators();
    }
    
    /**
     * Detects suspicious movement patterns
     */
    public boolean hasSuspiciousPattern() {
        if (movementHistory.size() < 10 || yawHistory.size() < 10) {
            return false;
        }
        
        // Check for repetitive circular movement
        boolean circularMovement = detectCircularMovement();
        
        // Check for back-and-forth movement
        boolean backAndForthMovement = detectBackAndForthMovement();
        
        // Check for robotic yaw changes
        boolean roboticYawChanges = detectRoboticYawChanges();
        
        suspiciousPatternDetected = circularMovement || backAndForthMovement || roboticYawChanges;
        return suspiciousPatternDetected;
    }
    
    // Helper methods
    private void addActivityScore(double points) {
        currentActivityScore = Math.min(100.0, currentActivityScore + points);
    }
    
    private void addToMovementHistory(Location location) {
        movementHistory.add(location.clone());
        if (movementHistory.size() > 20) {
            movementHistory.remove(0);
        }
    }
    
    private void addToYawHistory(float yaw) {
        yawHistory.add(yaw);
        if (yawHistory.size() > 20) {
            yawHistory.remove(0);
        }
    }
    
    private double getInteractionPoints(InteractionType type) {
        switch (type) {
            case BLOCK_BREAK: return 3.0;
            case BLOCK_PLACE: return 3.0;
            case INVENTORY_OPEN: return 8.0;
            case ITEM_USE: return 5.0;
            case COMMAND_USE: return 10.0;
            case CHAT: return 15.0;
            default: return 1.0;
        }
    }
    
    private void cleanupOldActivities() {
        long fiveMinutesAgo = System.currentTimeMillis() - 300000;
        recentActivities.removeIf(activity -> activity.getTimestamp() < fiveMinutesAgo);
    }
    
    private boolean detectCircularMovement() {
        // Implementation for detecting circular movement patterns
        // This is a simplified version - could be enhanced with more sophisticated algorithms
        if (movementHistory.size() < 8) return false;
        
        Location center = calculateCenterPoint();
        double avgDistance = 0;
        for (Location loc : movementHistory) {
            avgDistance += center.distance(loc);
        }
        avgDistance /= movementHistory.size();
        
        // Check if all points are roughly equidistant from center (circular pattern)
        int pointsNearAvgDistance = 0;
        for (Location loc : movementHistory) {
            double distance = center.distance(loc);
            if (Math.abs(distance - avgDistance) < 2.0) {
                pointsNearAvgDistance++;
            }
        }
        
        return pointsNearAvgDistance >= (movementHistory.size() * 0.7);
    }
    
    private boolean detectBackAndForthMovement() {
        if (movementHistory.size() < 6) return false;
        
        // Check for alternating movement between two points
        int alternatingCount = 0;
        for (int i = 2; i < movementHistory.size(); i++) {
            Location current = movementHistory.get(i);
            Location twoBack = movementHistory.get(i - 2);
            
            if (current.distance(twoBack) < 1.0) {
                alternatingCount++;
            }
        }
        
        return alternatingCount >= (movementHistory.size() * 0.5);
    }
    
    private boolean detectRoboticYawChanges() {
        if (yawHistory.size() < 10) return false;
        
        // Check for very regular yaw changes (robotic behavior)
        List<Float> yawDifferences = new ArrayList<>();
        for (int i = 1; i < yawHistory.size(); i++) {
            float diff = Math.abs(yawHistory.get(i) - yawHistory.get(i - 1));
            if (diff > 180) diff = 360 - diff; // Handle wrap-around
            yawDifferences.add(diff);
        }
        
        // Calculate standard deviation of yaw changes
        double mean = yawDifferences.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
        double variance = yawDifferences.stream().mapToDouble(f -> Math.pow(f - mean, 2)).average().orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        // Low standard deviation indicates very regular (robotic) movement
        return stdDev < 5.0 && mean > 10.0;
    }
    
    private Location calculateCenterPoint() {
        double x = 0, y = 0, z = 0;
        for (Location loc : movementHistory) {
            x += loc.getX();
            y += loc.getY();
            z += loc.getZ();
        }
        return new Location(movementHistory.get(0).getWorld(), 
                          x / movementHistory.size(), 
                          y / movementHistory.size(), 
                          z / movementHistory.size());
    }
    
    // Getters and setters
    public UUID getPlayerId() { return playerId; }
    public String getPlayerName() { return playerName; }
    public Location getLastKnownLocation() { return lastKnownLocation; }
    public long getLastMovementTime() { return lastMovementTime; }
    public long getLastLookChangeTime() { return lastLookChangeTime; }
    public long getLastInteractionTime() { return lastInteractionTime; }
    public double getCurrentActivityScore() { return currentActivityScore; }
    public int getStationaryChecks() { return stationaryChecks; }
    public boolean isSuspiciousPatternDetected() { return suspiciousPatternDetected; }
    public int getWarningCount() { return warningCount; }
    public double getPenaltyMultiplier() { return penaltyMultiplier; }
    
    public void addWarning() {
        warningCount++;
        lastWarningTime = System.currentTimeMillis();
    }
    
    public void resetWarnings() {
        warningCount = 0;
    }
    
    public void setPenaltyMultiplier(double multiplier) {
        this.penaltyMultiplier = Math.max(0.0, Math.min(1.0, multiplier));
    }
    
    public void resetActivityData() {
        currentActivityScore = 100.0;
        stationaryChecks = 0;
        warningCount = 0;
        penaltyMultiplier = 1.0;
        suspiciousPatternDetected = false;
        recentActivities.clear();
        movementHistory.clear();
        yawHistory.clear();
    }
    
    // Inner classes
    public enum InteractionType {
        BLOCK_BREAK,
        BLOCK_PLACE,
        INVENTORY_OPEN,
        ITEM_USE,
        COMMAND_USE,
        CHAT
    }
    
    public static class ActivityEvent {
        private final InteractionType type;
        private final long timestamp;
        
        public ActivityEvent(InteractionType type, long timestamp) {
            this.type = type;
            this.timestamp = timestamp;
        }
        
        public InteractionType getType() { return type; }
        public long getTimestamp() { return timestamp; }
    }
}
