package it.masterzen.minebuddy;

import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.entity.Player;

/**
 * Manager class for MineBuddy upgrade system
 * Handles upgrade calculations, bonuses, and purchase validation
 */
public class MineBuddyUpgradeManager {
    
    private final AlphaBlockBreak plugin;
    private final String prefix = "§e§lMINE BUDDY §8»§7 ";
    
    // Base price for upgrades
    private static final long BASE_UPGRADE_PRICE = 100000L;
    
    // Bonus percentages per upgrade level
    private static final double MINE_BONUS_PER_LEVEL = 0.20; // 20% per level
    private static final double FARMER_DUNGEON_BONUS_PER_LEVEL = 0.10; // 10% per level
    
    public MineBuddyUpgradeManager(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Calculates the price for the next upgrade level for a specific location type
     * Formula: basePrice (100,000) + (currentLevel * 10,000 * blocksMined)
     * 
     * @param player The player
     * @param locationType The location type (MINE, FARMER_WARP, DUNGEON)
     * @return The price for the next upgrade level
     */
    public long calculateUpgradePrice(Player player, MineBuddy.LocationType locationType) {
        PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
        if (playerData == null) {
            return BASE_UPGRADE_PRICE;
        }
        
        int currentLevel = getCurrentUpgradeLevel(playerData, locationType);
        return playerData.calculateMineBuddyUpgradePrice(currentLevel);
    }
    
    /**
     * Gets the current upgrade level for a specific location type
     * 
     * @param playerData The player data
     * @param locationType The location type
     * @return The current upgrade level
     */
    public int getCurrentUpgradeLevel(PlayerData playerData, MineBuddy.LocationType locationType) {
        if (playerData == null) {
            return 0;
        }
        
        switch (locationType) {
            case MINE:
                return playerData.getMineBuddyMineUpgradeLevel();
            case FARMER_WARP:
                return playerData.getMineBuddyFarmerUpgradeLevel();
            case DUNGEON:
                return playerData.getMineBuddyDungeonUpgradeLevel();
            default:
                return 0;
        }
    }
    
    /**
     * Calculates the total bonus multiplier for a specific location type
     * 
     * @param playerData The player data
     * @param locationType The location type
     * @return The bonus multiplier (1.0 = no bonus, 1.2 = 20% bonus, etc.)
     */
    public double calculateBonusMultiplier(PlayerData playerData, MineBuddy.LocationType locationType) {
        if (playerData == null) {
            return 1.0;
        }
        
        int upgradeLevel = getCurrentUpgradeLevel(playerData, locationType);
        if (upgradeLevel <= 0) {
            return 1.0;
        }
        
        double bonusPerLevel = getBonusPerLevel(locationType);
        return 1.0 + (upgradeLevel * bonusPerLevel);
    }
    
    /**
     * Gets the bonus percentage per level for a specific location type
     * 
     * @param locationType The location type
     * @return The bonus percentage per level
     */
    private double getBonusPerLevel(MineBuddy.LocationType locationType) {
        switch (locationType) {
            case MINE:
                return MINE_BONUS_PER_LEVEL;
            case FARMER_WARP:
            case DUNGEON:
                return FARMER_DUNGEON_BONUS_PER_LEVEL;
            default:
                return 0.0;
        }
    }
    
    /**
     * Applies upgrade bonus to block count
     * 
     * @param baseBlockCount The base block count
     * @param playerData The player data
     * @param locationType The location type
     * @return The modified block count with bonuses applied
     */
    public int applyUpgradeBonus(int baseBlockCount, PlayerData playerData, MineBuddy.LocationType locationType) {
        if (playerData == null || baseBlockCount <= 0) {
            return baseBlockCount;
        }
        
        double multiplier = calculateBonusMultiplier(playerData, locationType);
        int bonusBlocks = (int) Math.ceil(baseBlockCount * multiplier);
        
        // Log the bonus application for debugging
        if (multiplier > 1.0) {
            plugin.getLogger().fine("Applied MineBuddy upgrade bonus: " + baseBlockCount + " -> " + bonusBlocks + 
                                  " (x" + String.format("%.2f", multiplier) + ") for " + locationType);
        }
        
        return bonusBlocks;
    }
    
    /**
     * Validates if a player can purchase an upgrade
     * 
     * @param player The player
     * @param locationType The location type
     * @return True if the player can purchase the upgrade, false otherwise
     */
    public boolean canPurchaseUpgrade(Player player, MineBuddy.LocationType locationType) {
        PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
        if (playerData == null) {
            return false;
        }
        
        long upgradePrice = calculateUpgradePrice(player, locationType);
        long playerBlocks = playerData.getBlocksMined() != null ? playerData.getBlocksMined() : 0;
        
        return playerBlocks >= upgradePrice;
    }
    
    /**
     * Processes an upgrade purchase
     * 
     * @param player The player
     * @param locationType The location type
     * @return True if the purchase was successful, false otherwise
     */
    public boolean purchaseUpgrade(Player player, MineBuddy.LocationType locationType) {
        PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
        if (playerData == null) {
            player.sendMessage(prefix + "§cError: Could not load your data!");
            return false;
        }
        
        if (!canPurchaseUpgrade(player, locationType)) {
            long upgradePrice = calculateUpgradePrice(player, locationType);
            player.sendMessage(prefix + "§cYou need §f" + plugin.newFormatNumber(upgradePrice, player) + " §cblocks to purchase this upgrade!");
            return false;
        }
        
        long upgradePrice = calculateUpgradePrice(player, locationType);
        int currentLevel = getCurrentUpgradeLevel(playerData, locationType);
        
        // Deduct the cost
        playerData.addBlocksMined((int) -upgradePrice);
        
        // Increase the upgrade level
        switch (locationType) {
            case MINE:
                playerData.addMineBuddyMineUpgradeLevel(1);
                break;
            case FARMER_WARP:
                playerData.addMineBuddyFarmerUpgradeLevel(1);
                break;
            case DUNGEON:
                playerData.addMineBuddyDungeonUpgradeLevel(1);
                break;
        }
        
        // Save the data
        try {
            plugin.getMongoReader().savePlayerData(playerData, false);
            
            // Send success message
            int newLevel = currentLevel + 1;
            double bonusPerLevel = getBonusPerLevel(locationType) * 100; // Convert to percentage
            player.sendMessage(prefix + "§aUpgrade purchased! §7" + getLocationDisplayName(locationType) + 
                             " §7is now level §a" + newLevel + " §7(+" + (int)bonusPerLevel + "% per level)");
            
            plugin.getLogger().info("Player " + player.getName() + " purchased MineBuddy upgrade for " + 
                                  locationType + " (Level " + newLevel + ") for " + upgradePrice + " blocks");
            
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Error saving MineBuddy upgrade for player " + player.getName() + ": " + e.getMessage());
            player.sendMessage(prefix + "§cError: Could not save your upgrade! Please try again.");
            return false;
        }
    }
    
    /**
     * Gets the display name for a location type
     * 
     * @param locationType The location type
     * @return The display name
     */
    public String getLocationDisplayName(MineBuddy.LocationType locationType) {
        switch (locationType) {
            case MINE:
                return "§6Mine";
            case FARMER_WARP:
                return "§aFarmer";
            case DUNGEON:
                return "§5Dungeon";
            default:
                return "§7Unknown";
        }
    }
    
    /**
     * Gets the bonus percentage display for a location type
     * 
     * @param locationType The location type
     * @return The bonus percentage as a string
     */
    public String getBonusPercentageDisplay(MineBuddy.LocationType locationType) {
        double bonus = getBonusPerLevel(locationType) * 100;
        return (int)bonus + "%";
    }
}
