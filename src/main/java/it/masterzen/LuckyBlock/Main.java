package it.masterzen.LuckyBlock;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.ItemNames;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class Main {

    public final AlphaBlockBreak mainClass;
    public final RewardList rewardList;
    private static YamlConfiguration ymlFile;
    List<LuckyBlockReward> luckyBlockRewards = new ArrayList<>();

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        rewardList = new RewardList();
    }

    public void loadRewards() throws IOException {
        File tmpFile;
        tmpFile = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/LuckyBlockRewards.yml");
        ymlFile = YamlConfiguration.loadConfiguration(tmpFile);
        String path = "";

        if (!tmpFile.exists()) {
            changeRewards();
        } else {
            if (ymlFile.contains("first")) {
                for (int i = 0; i < 3; i++) {
                    if (i == 0) {
                        path = "first";
                    } else if (i == 1) {
                        path = "second";
                    } else {
                        path = "third";
                    }

                    luckyBlockRewards.add(rewardList.getRewardList().get(ymlFile.getInt(path + ".id")));
                }
            } else {
                changeRewards();
            }
        }
    }

    public void changeRewards() throws IOException {
        List<Integer> tmpIDList = new ArrayList<>();
        File tmpFile;
        tmpFile = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/LuckyBlockRewards.yml");
        ymlFile = YamlConfiguration.loadConfiguration(tmpFile);
        String path = "";

        tmpFile.delete();
        tmpFile.createNewFile();

        luckyBlockRewards = new ArrayList<>();
        //luckyBlockRewards.add(rewardList.getRewardList().get(0));
        //ymlFile.set("first.id", 1);
        tmpIDList.add(0);

        int maxIterations = 100;
        while (luckyBlockRewards.size() < 3 && maxIterations > 0) {
            int id = ThreadLocalRandom.current().nextInt(rewardList.getRewardList().size());
            if (!tmpIDList.contains(id)) {
                luckyBlockRewards.add(rewardList.getRewardList().get(id));
                //if (tmpIDList.size() == 1) {
                //    ymlFile.set("second.id", id);
                //} else {
                //    ymlFile.set("third.id", id);
                //}
                tmpIDList.add(id);
            }
            maxIterations--;
        }

        // ORDINO LA LISTA PER AVERE LE CHANCE IN ORDINE
        HashMap<Integer, Double> tmpMap = new HashMap<>();
        for (LuckyBlockReward reward : luckyBlockRewards) {
            tmpMap.put(reward.getId(), reward.getChance());
        }

        tmpMap = sortProgress(tmpMap);
        luckyBlockRewards.clear();
        int size = 1;
        for (Integer id : tmpMap.keySet()) {
            id--;
            luckyBlockRewards.add(rewardList.getRewardList().get(id));
            if (size == 1) {
                ymlFile.set("first.id", id);
            } else if (size == 2) {
                ymlFile.set("second.id", id);
            } else {
                ymlFile.set("third.id", id);
            }
            size++;
        }

        ymlFile.save(tmpFile);

        for (Player player : Bukkit.getOnlinePlayers()) {
            sendRewardList(player);
        }
    }

    public void giveLuckyBlockReward(Player player, boolean message) {
        double chance = ThreadLocalRandom.current().nextDouble(100) + 1; // 1 -> 100

        String command = "";
        for (LuckyBlockReward reward : luckyBlockRewards) {
            if (chance <= reward.getChance()) {
                if (command.equals("")) {
                    command = reward.getCommandToExecute();
                }
            }
        }

        // SE NESSUNA CHANCE VA BENE ALLORA DO KEYS
        /*if (command.equals("")) {
            command = "giveKeys";
        }*/
        if (command.equals("")) {
            //command = luckyBlockRewards.get(ThreadLocalRandom.current().nextInt(luckyBlockRewards.size())).getCommandToExecute();
            return;
        }

        switch (command) {
            case "giveKeys":
                mainClass.getKeysManager().giveRandomKeys(player, 2, 1);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §7Keys (LB)"));
                break;
            case "giveBeacon":
                double luckyBeaconLevel = mainClass.getEnchantLevel(player, "Lucky Beacon");
                long totalBeacons = (long) (8 + luckyBeaconLevel);
                mainClass.getBeaconBackpack().addBeacons(player, totalBeacons);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §7" + totalBeacons + " Beacons (LB)"));
                break;
            case "giveArmorPoints":
                mainClass.getArmorPointsSystem().addPoints(player, 1);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §71 Armor Point (LB)"));
                break;
            case "giveCrystals":
                int tier = ThreadLocalRandom.current().nextInt(3) + 1;
                int amount = 0;
                if (tier == 1) {
                    amount = ThreadLocalRandom.current().nextInt(100) + 50;
                } else if (tier == 2) {
                    amount = ThreadLocalRandom.current().nextInt(50) + 50;
                } else {
                    amount = ThreadLocalRandom.current().nextInt(25) + 25;
                }
                long finalPoints = mainClass.getDungeonSystem().addPoints(player, tier, amount);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §7" + finalPoints + " Tier " + tier + " Crystals"));
                break;
            case "giveSpawnerPoints":
                mainClass.addSpawnerShard(player, ThreadLocalRandom.current().nextInt(10) + 10);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §710 Spawner Points (LB)"));
                break;
            case "giveCandy":
                int candy = ThreadLocalRandom.current().nextInt(2) + 1;
                mainClass.getCandyFinderSystem().addCandy(player, candy, false);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §7" + candy + " Candys (LB)"));
                break;
            case "giveFarmerPoints":
                int crops = ThreadLocalRandom.current().nextInt(15) + 10;
                mainClass.addCropsPoints(player, crops, false);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ §7" + crops + " Farmer Points (LB)"));
                break;
        }
    }

    public void sendRewardList(Player player) {
        player.sendMessage("");
        player.sendMessage("§e§lLUCKYBLOCK §7| Reward List");
        for (LuckyBlockReward reward : luckyBlockRewards) {
            player.sendMessage("§f" + reward.getName() + "§7 for a chance of §a§l" + reward.getChance() + "%");
        }
        player.sendMessage("");
    }

    // function to sort hashmap by values
    public static HashMap<Integer, Double> sortProgress(HashMap<Integer, Double> hm) {
        // Create a list from elements of HashMap
        List<Map.Entry<Integer, Double> > list = new LinkedList<Map.Entry<Integer, Double> >(hm.entrySet());

        // Sort the list
        list.sort(new Comparator<Map.Entry<Integer, Double>>() {
            public int compare(Map.Entry<Integer, Double> o1, Map.Entry<Integer, Double> o2) {
                return (o1.getValue()).compareTo(o2.getValue());
            }
        });

        // put data from sorted list to hashmap
        HashMap<Integer, Double> temp = new LinkedHashMap<Integer, Double>();
        for (Map.Entry<Integer, Double> aa : list) {
            temp.put(aa.getKey(), aa.getValue());
        }
        return temp;
    }
}
