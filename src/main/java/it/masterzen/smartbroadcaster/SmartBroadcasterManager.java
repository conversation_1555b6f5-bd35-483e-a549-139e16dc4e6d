package it.masterzen.smartbroadcaster;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.io.IOException;
import java.util.logging.Level;

/**
 * Manager class for the Smart Broadcaster system.
 * Handles configuration, scheduling, and integration with plugin lifecycle.
 */
public class SmartBroadcasterManager implements Listener {
    
    private final AlphaBlockBreak plugin;
    private final SmartBroadcaster broadcaster;
    private final SmartBroadcasterCommand command;
    
    // Configuration
    private File configFile;
    private FileConfiguration config;
    
    // Scheduling
    private BukkitTask broadcastTask;
    private BukkitTask cleanupTask;
    
    // Settings
    private boolean enabled = true;
    private long broadcastIntervalMinutes = 8; // 8 minutes between broadcasts
    private long cleanupIntervalHours = 6; // Clean up old data every 6 hours
    
    public SmartBroadcasterManager(AlphaBlockBreak plugin) {
        this.plugin = plugin;
        this.broadcaster = new SmartBroadcaster(plugin);
        this.command = new SmartBroadcasterCommand(plugin, this);

        setupConfig();
        loadConfig();
        initializeFeatureTips();

        // Register events
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // Register command
        if (plugin.getCommand("smarttips") != null) {
            plugin.getCommand("smarttips").setExecutor(command);
        }
    }
    
    /**
     * Starts the broadcaster system.
     */
    public void start() {
        if (!enabled) {
            plugin.getLogger().info("Smart Broadcaster is disabled in configuration");
            return;
        }
        
        // Start broadcast task
        if (broadcastTask != null) {
            broadcastTask.cancel();
        }
        
        long intervalTicks = broadcastIntervalMinutes * 60 * 20; // Convert minutes to ticks
        broadcastTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (broadcaster.isEnabled()) {
                    broadcaster.processAllPlayers();
                }
            }
        }.runTaskTimer(plugin, intervalTicks, intervalTicks); // Start after first interval, repeat
        
        // Start cleanup task
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }
        
        long cleanupTicks = cleanupIntervalHours * 60 * 60 * 20; // Convert hours to ticks
        cleanupTask = new BukkitRunnable() {
            @Override
            public void run() {
                broadcaster.cleanupOldData();
            }
        }.runTaskTimer(plugin, cleanupTicks, cleanupTicks);
        
        plugin.getLogger().info("Smart Broadcaster started - broadcasting every " + broadcastIntervalMinutes + " minutes");
    }
    
    /**
     * Stops the broadcaster system.
     */
    public void stop() {
        if (broadcastTask != null) {
            broadcastTask.cancel();
            broadcastTask = null;
        }
        
        if (cleanupTask != null) {
            cleanupTask.cancel();
            cleanupTask = null;
        }
        
        plugin.getLogger().info("Smart Broadcaster stopped");
    }
    
    /**
     * Sets up the configuration file.
     */
    private void setupConfig() {
        configFile = new File(plugin.getDataFolder(), "smartbroadcaster.yml");
        if (!configFile.exists()) {
            plugin.getDataFolder().mkdirs();
            createDefaultConfig();
        }
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * Creates the default configuration file.
     */
    private void createDefaultConfig() {
        try {
            configFile.createNewFile();
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);
            
            // General settings
            defaultConfig.set("enabled", true);
            defaultConfig.set("broadcast-interval-minutes", 8);
            defaultConfig.set("cleanup-interval-hours", 6);
            defaultConfig.set("global-cooldown-minutes", 10);
            defaultConfig.set("max-tips-per-session", 3);
            
            // Tip settings
            defaultConfig.set("tips.private-mine.enabled", true);
            defaultConfig.set("tips.private-mine.priority", 10);
            defaultConfig.set("tips.private-mine.cooldown-minutes", 60);
            
            defaultConfig.set("tips.mine-buddy.enabled", true);
            defaultConfig.set("tips.mine-buddy.priority", 8);
            defaultConfig.set("tips.mine-buddy.cooldown-minutes", 45);

            defaultConfig.set("tips.mine-buddy-upgrades.enabled", true);
            defaultConfig.set("tips.mine-buddy-upgrades.priority", 7);
            defaultConfig.set("tips.mine-buddy-upgrades.cooldown-minutes", 60);

            defaultConfig.set("tips.alpha-discount.enabled", true);
            defaultConfig.set("tips.alpha-discount.priority", 6);
            defaultConfig.set("tips.alpha-discount.cooldown-minutes", 90);
            
            defaultConfig.set("tips.mine-sharing.enabled", true);
            defaultConfig.set("tips.mine-sharing.priority", 5);
            defaultConfig.set("tips.mine-sharing.cooldown-minutes", 120);
            
            defaultConfig.set("tips.worker-afk.enabled", true);
            defaultConfig.set("tips.worker-afk.priority", 7);
            defaultConfig.set("tips.worker-afk.cooldown-minutes", 75);
            
            // Custom Crops tip
            defaultConfig.set("tips.custom-crops.enabled", true);
            defaultConfig.set("tips.custom-crops.priority", 9);
            defaultConfig.set("tips.custom-crops.cooldown-minutes", 60);

            // Class System tip
            defaultConfig.set("tips.class-system.enabled", true);
            defaultConfig.set("tips.class-system.priority", 8);
            defaultConfig.set("tips.class-system.cooldown-minutes", 90);

            // Skill System tip
            defaultConfig.set("tips.skill-system.enabled", true);
            defaultConfig.set("tips.skill-system.priority", 8);
            defaultConfig.set("tips.skill-system.cooldown-minutes", 90);

            // Custom Recipes tip
            defaultConfig.set("tips.custom-recipes.enabled", true);
            defaultConfig.set("tips.custom-recipes.priority", 7);
            defaultConfig.set("tips.custom-recipes.cooldown-minutes", 80);

            defaultConfig.save(configFile);
            plugin.getLogger().info("Created default Smart Broadcaster configuration");
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create Smart Broadcaster configuration", e);
        }
    }
    
    /**
     * Loads configuration from file.
     */
    private void loadConfig() {
        config = YamlConfiguration.loadConfiguration(configFile);
        
        enabled = config.getBoolean("enabled", true);
        broadcastIntervalMinutes = config.getLong("broadcast-interval-minutes", 8);
        cleanupIntervalHours = config.getLong("cleanup-interval-hours", 6);
        
        // Apply settings to broadcaster
        broadcaster.setEnabled(enabled);
        broadcaster.setGlobalCooldownMinutes(config.getLong("global-cooldown-minutes", 10));
        broadcaster.setMaxTipsPerSession(config.getInt("max-tips-per-session", 3));
        
        plugin.getLogger().info("Smart Broadcaster configuration loaded");
    }
    
    /**
     * Initializes all feature tips.
     */
    private void initializeFeatureTips() {
        // Private Mine tip
        if (config.getBoolean("tips.private-mine.enabled", true)) {
            FeatureTip privateMine = new FeatureTip(
                "private-mine",
                "§e§lSMART TIP §8»§7 §6Private Mine",
                "§7Did you know? The §aPrivate Mine §7is the best mine on the server and it's §apermanent§7! Get yours with §f/buy§7.",
                    player -> !hasPrivateMine(player), // Show only to non private mine owners
                config.getInt("tips.private-mine.priority", 10),
                config.getLong("tips.private-mine.cooldown-minutes", 60)
            );
            broadcaster.addFeatureTip(privateMine);
        }
        
        // Mine Buddy tip
        if (config.getBoolean("tips.mine-buddy.enabled", true)) {
            FeatureTip mineBuddy = new FeatureTip(
                "mine-buddy",
                "§e§lSMART TIP §8»§7 §bMine Buddy",
                "§7Need help mining? §bMine Buddy §7can assist you in mines, dungeons, and the farmer warp! It works automatically when you're in the right place (you can AFK it too!). §6Upgrade it with §f/minebuddyupgrade §6for better efficiency!",
                player -> true, // Show to all players
                config.getInt("tips.mine-buddy.priority", 8),
                config.getLong("tips.mine-buddy.cooldown-minutes", 45)
            );
            broadcaster.addFeatureTip(mineBuddy);
        }

        // MineBuddy Upgrades tip
        if (config.getBoolean("tips.mine-buddy-upgrades.enabled", true)) {
            FeatureTip mineBuddyUpgrades = new FeatureTip(
                "mine-buddy-upgrades",
                "§e§lSMART TIP §8»§7 §6MineBuddy Upgrades",
                "§7Boost your §bMineBuddy §7efficiency! Use §f/minebuddyupgrade §7to purchase upgrades that permanently increase block breaking: §6+20% per level for mines§7, §a+10% for farmer/dungeons§7. Upgrades cost blocks you've mined!",
                player -> true, // Show to all players
                config.getInt("tips.mine-buddy-upgrades.priority", 7),
                config.getLong("tips.mine-buddy-upgrades.cooldown-minutes", 60)
            );
            broadcaster.addFeatureTip(mineBuddyUpgrades);
        }

        // Alpha+ discount tip
        if (config.getBoolean("tips.alpha-discount.enabled", true)) {
            FeatureTip alphaDiscount = new FeatureTip(
                "alpha-discount",
                "§e§lSMART TIP §8»§7 §dAlpha+ Benefits",
                "§7As an §dAlpha+ §7member, you get a §a10% discount §7on all enchantments! Make sure to take advantage of this exclusive benefit.",
                player -> true, // Show to all players
                config.getInt("tips.alpha-discount.priority", 6),
                config.getLong("tips.alpha-discount.cooldown-minutes", 90)
            );
            broadcaster.addFeatureTip(alphaDiscount);
        }
        
        // Mine sharing tip
        if (config.getBoolean("tips.mine-sharing.enabled", true)) {
            FeatureTip mineSharing = new FeatureTip(
                "mine-sharing",
                "§e§lSMART TIP §8»§7 §aMine Sharing",
                "§7You can open your mine to other players and set taxes! Use §f/mine open §7and §f/mine settaxes [amount] §7to earn from visitors.",
                // player -> hasPrivateMine(player), // Show only to private mine owners
                player -> true, // Show to all players
                config.getInt("tips.mine-sharing.priority", 5),
                config.getLong("tips.mine-sharing.cooldown-minutes", 120)
            );
            broadcaster.addFeatureTip(mineSharing);
        }
        
        // Worker AFK tip
        if (config.getBoolean("tips.worker-afk.enabled", true)) {
            FeatureTip workerAfk = new FeatureTip(
                "worker-afk",
                "§e§lSMART TIP §8»§7 §cWorker System",
                "§7Want to earn resources while AFK? Use §f/worker §7to access the AFK resource system and keep earning even when you're away!",
                player -> true, // Show to all players
                config.getInt("tips.worker-afk.priority", 7),
                config.getLong("tips.worker-afk.cooldown-minutes", 75)
            );
            broadcaster.addFeatureTip(workerAfk);
        }
        
        // Custom Crops tip
        if (config.getBoolean("tips.custom-crops.enabled", true)) {
            FeatureTip customCrops = new FeatureTip(
                "custom-crops",
                "§e§lSMART TIP §8»§7 §aCustom Crops",
                "§7Custom Crops §7are an §eexcellent solution §7to obtain §bAFK resources§7! Try them now with §f/crystal shop.",
                player -> true, // Show to all players
                config.getInt("tips.custom-crops.priority", 9),
                config.getLong("tips.custom-crops.cooldown-minutes", 60)
            );
            broadcaster.addFeatureTip(customCrops);
        }

        // Class System tip
        if (config.getBoolean("tips.class-system.enabled", true)) {
            FeatureTip classSystem = new FeatureTip(
                "class-system",
                "§e§lSMART TIP §8»§7 §dClass System",
                "§7Choose your class to get special bonuses! Use §f/class [MERCHANT|MINER|TREASURER|EXPERIENCER] §7to select. Each class gives unique multipliers and benefits!",
                player -> true, // Show to all players
                config.getInt("tips.class-system.priority", 8),
                config.getLong("tips.class-system.cooldown-minutes", 90)
            );
            broadcaster.addFeatureTip(classSystem);
        }

        // Skill System tip
        if (config.getBoolean("tips.skill-system.enabled", true)) {
            FeatureTip skillSystem = new FeatureTip(
                "skill-system",
                "§e§lSMART TIP §8»§7 §bSkill System",
                "§7Upgrade your abilities with the Skill Tree! Use §f/skill §7or §f/skills §7to access the skill system and spend your skill points on powerful upgrades!",
                player -> true, // Show to all players
                config.getInt("tips.skill-system.priority", 8),
                config.getLong("tips.skill-system.cooldown-minutes", 90)
            );
            broadcaster.addFeatureTip(skillSystem);
        }

        // Custom Recipes tip
        if (config.getBoolean("tips.custom-recipes.enabled", true)) {
            FeatureTip customRecipes = new FeatureTip(
                "custom-recipes",
                "§e§lSMART TIP §8»§7 §6Custom Recipes",
                "§7Discover powerful custom crafting recipes! Use §f/recipes §7to view all available recipes including refillable bombs, pet eggs, and special items. Craft with Dungeon ores!",
                player -> true, // Show to all players
                config.getInt("tips.custom-recipes.priority", 7),
                config.getLong("tips.custom-recipes.cooldown-minutes", 80)
            );
            broadcaster.addFeatureTip(customRecipes);
        }

        plugin.getLogger().info("Initialized " + broadcaster.getFeatureTips().size() + " feature tips");
    }
    
    /**
     * Checks if a player has a private mine.
     *
     * @param player The player to check
     * @return true if player has a private mine
     */
    private boolean hasPrivateMine(Player player) {
        try {
            // Check if player has private mine permission
            return player.hasPermission("privatemine.created");
        } catch (Exception e) {
            // Fallback: assume they don't have a private mine if we can't check
            plugin.getLogger().warning("Error checking private mine for player " + player.getName() + ": " + e.getMessage());
            return false;
        }
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Reset session data for returning players
        broadcaster.resetPlayerSession(event.getPlayer().getUniqueId());
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up session data but keep cooldowns
        broadcaster.resetPlayerSession(event.getPlayer().getUniqueId());
    }
    
    /**
     * Reloads the configuration.
     */
    public void reloadConfig() {
        loadConfig();
        
        // Restart with new settings
        if (enabled) {
            stop();
            start();
        }
    }
    
    // Getters
    public SmartBroadcaster getBroadcaster() {
        return broadcaster;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
}
