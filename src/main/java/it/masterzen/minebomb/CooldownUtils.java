package it.masterzen.minebomb;

import java.util.concurrent.TimeUnit;

/**
 * Utility class for handling cooldown calculations and time formatting
 */
public class CooldownUtils {

    // Cooldown limits (in minutes)
    public static final int MIN_COOLDOWN_MINUTES = 1;
    public static final int MAX_COOLDOWN_MINUTES = 1440; // 24 hours
    public static final int DEFAULT_COOLDOWN_MINUTES = 60; // 1 hour

    /**
     * Validates if a cooldown value is within acceptable limits
     * @param cooldownMinutes The cooldown in minutes
     * @return true if valid, false otherwise
     */
    public static boolean isValidCooldown(int cooldownMinutes) {
        return cooldownMinutes >= MIN_COOLDOWN_MINUTES && cooldownMinutes <= MAX_COOLDOWN_MINUTES;
    }

    /**
     * Converts minutes to milliseconds
     * @param minutes The minutes to convert
     * @return milliseconds
     */
    public static long minutesToMillis(int minutes) {
        return TimeUnit.MINUTES.toMillis(minutes);
    }

    /**
     * Converts milliseconds to minutes
     * @param millis The milliseconds to convert
     * @return minutes
     */
    public static long millisToMinutes(long millis) {
        return TimeUnit.MILLISECONDS.toMinutes(millis);
    }

    /**
     * Formats remaining time into a human-readable string
     * @param remainingMillis The remaining time in milliseconds
     * @return formatted time string (e.g., "5m 30s", "2h 15m", "1d 3h")
     */
    public static String formatRemainingTime(long remainingMillis) {
        if (remainingMillis <= 0) {
            return "0s";
        }

        long days = TimeUnit.MILLISECONDS.toDays(remainingMillis);
        long hours = TimeUnit.MILLISECONDS.toHours(remainingMillis) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(remainingMillis) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(remainingMillis) % 60;

        StringBuilder result = new StringBuilder();

        if (days > 0) {
            result.append(days).append("d ");
        }
        if (hours > 0) {
            result.append(hours).append("h ");
        }
        if (minutes > 0) {
            result.append(minutes).append("m ");
        }
        if (seconds > 0 && days == 0) { // Only show seconds if less than a day
            result.append(seconds).append("s");
        }

        return result.toString().trim();
    }

    /**
     * Formats cooldown duration for display in lore
     * @param cooldownMinutes The cooldown in minutes
     * @return formatted cooldown string (e.g., "5 minutes", "2 hours", "1 day")
     */
    public static String formatCooldownDuration(int cooldownMinutes) {
        if (cooldownMinutes < 60) {
            return cooldownMinutes + (cooldownMinutes == 1 ? " minute" : " minutes");
        } else if (cooldownMinutes < 1440) {
            int hours = cooldownMinutes / 60;
            int remainingMinutes = cooldownMinutes % 60;
            String result = hours + (hours == 1 ? " hour" : " hours");
            if (remainingMinutes > 0) {
                result += " " + remainingMinutes + (remainingMinutes == 1 ? " minute" : " minutes");
            }
            return result;
        } else {
            int days = cooldownMinutes / 1440;
            int remainingHours = (cooldownMinutes % 1440) / 60;
            String result = days + (days == 1 ? " day" : " days");
            if (remainingHours > 0) {
                result += " " + remainingHours + (remainingHours == 1 ? " hour" : " hours");
            }
            return result;
        }
    }

    /**
     * Checks if enough time has passed since the last use
     * @param lastUseTime The timestamp of last use
     * @param cooldownMillis The cooldown duration in milliseconds
     * @return true if cooldown has expired, false otherwise
     */
    public static boolean isCooldownExpired(long lastUseTime, long cooldownMillis) {
        return System.currentTimeMillis() - lastUseTime >= cooldownMillis;
    }

    /**
     * Gets the remaining cooldown time in milliseconds
     * @param lastUseTime The timestamp of last use
     * @param cooldownMillis The cooldown duration in milliseconds
     * @return remaining time in milliseconds, or 0 if cooldown has expired
     */
    public static long getRemainingCooldown(long lastUseTime, long cooldownMillis) {
        long elapsed = System.currentTimeMillis() - lastUseTime;
        long remaining = cooldownMillis - elapsed;
        return Math.max(0, remaining);
    }

    /**
     * Parses a cooldown string (e.g., "5m", "2h", "1d") to minutes
     * @param cooldownStr The cooldown string
     * @return cooldown in minutes, or -1 if invalid
     */
    public static int parseCooldownString(String cooldownStr) {
        if (cooldownStr == null || cooldownStr.trim().isEmpty()) {
            return -1;
        }

        cooldownStr = cooldownStr.trim().toLowerCase();
        
        try {
            if (cooldownStr.endsWith("m")) {
                return Integer.parseInt(cooldownStr.substring(0, cooldownStr.length() - 1));
            } else if (cooldownStr.endsWith("h")) {
                return Integer.parseInt(cooldownStr.substring(0, cooldownStr.length() - 1)) * 60;
            } else if (cooldownStr.endsWith("d")) {
                return Integer.parseInt(cooldownStr.substring(0, cooldownStr.length() - 1)) * 1440;
            } else {
                // Assume minutes if no suffix
                return Integer.parseInt(cooldownStr);
            }
        } catch (NumberFormatException e) {
            return -1;
        }
    }
}
