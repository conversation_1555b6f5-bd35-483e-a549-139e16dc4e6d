package it.masterzen.minebomb;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class RefillableMineBombCommand implements CommandExecutor {

    private final AlphaBlockBreak plugin;

    public RefillableMineBombCommand(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("minebomb.refillable.give")) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cYou don't have permission to use this command.");
            return true;
        }

        if (args.length < 4) {
            sendUsage(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "give":
                return handleGiveCommand(sender, args);
            case "reset":
                return handleResetCommand(sender, args);
            case "info":
                return handleInfoCommand(sender, args);
            default:
                sendUsage(sender);
                return true;
        }
    }

    private boolean handleGiveCommand(CommandSender sender, String[] args) {
        if (args.length < 5) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cUsage: /refillableminebomb give <player> <money|tokens> <tier 1-3> <cooldown>");
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §7Cooldown format: 5m, 2h, 1d (minutes/hours/days)");
            return true;
        }

        String targetName = args[1];
        String typeStr = args[2].toLowerCase();
        String tierStr = args[3];
        String cooldownStr = args[4];

        Player target = Bukkit.getPlayer(targetName);
        if (target == null) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cPlayer not found: " + targetName);
            return true;
        }

        RefillableBombType type = RefillableBombType.fromString(typeStr);
        if (type == null) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid type. Use 'money' or 'tokens'.");
            return true;
        }

        int tierInt;
        try {
            tierInt = Integer.parseInt(tierStr);
        } catch (NumberFormatException e) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid tier. Use 1, 2, or 3.");
            return true;
        }

        BombTier tier = BombTier.fromInt(tierInt);
        if (tier == null) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid tier. Use 1, 2, or 3.");
            return true;
        }

        int cooldownMinutes = CooldownUtils.parseCooldownString(cooldownStr);
        if (cooldownMinutes < 0) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid cooldown format. Use: 5m, 2h, 1d");
            return true;
        }

        if (!CooldownUtils.isValidCooldown(cooldownMinutes)) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cCooldown must be between " + 
                CooldownUtils.MIN_COOLDOWN_MINUTES + " and " + CooldownUtils.MAX_COOLDOWN_MINUTES + " minutes.");
            return true;
        }

        try {
            target.getInventory().addItem(MineBombItem.createRefillable(type, tier, cooldownMinutes));
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §aGave refillable §7T" + tier.getLevel() +
                " " + type.getDisplayName().toLowerCase() + " bomb to §f" + target.getName() +
                " §7with §e" + CooldownUtils.formatCooldownDuration(cooldownMinutes) + " §7cooldown");

            if (!sender.equals(target)) {
                target.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §aYou received a refillable mine bomb!");
            }
        } catch (Exception e) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cError creating refillable mine bomb: " + e.getMessage());
            plugin.getLogger().warning("Error creating refillable mine bomb: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    private boolean handleResetCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cUsage: /refillableminebomb reset <player>");
            return true;
        }

        if (!sender.hasPermission("minebomb.refillable.reset")) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cYou don't have permission to reset cooldowns.");
            return true;
        }

        String targetName = args[1];
        Player target = Bukkit.getPlayer(targetName);
        if (target == null) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cPlayer not found: " + targetName);
            return true;
        }

        int resetCount = 0;
        for (int i = 0; i < target.getInventory().getSize(); i++) {
            org.bukkit.inventory.ItemStack item = target.getInventory().getItem(i);
            if (MineBombItem.isRefillableMineBomb(item)) {
                target.getInventory().setItem(i, MineBombItem.resetCooldown(item));
                resetCount++;
            }
        }

        if (resetCount > 0) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §aReset cooldown for " + resetCount + 
                " refillable mine bomb(s) for §f" + target.getName());
            target.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §aYour refillable mine bomb cooldowns have been reset!");
        } else {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cNo refillable mine bombs found in " + target.getName() + "'s inventory.");
        }

        return true;
    }

    private boolean handleInfoCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cThis command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;
        org.bukkit.inventory.ItemStack item = player.getInventory().getItemInMainHand();
        
        if (!MineBombItem.isRefillableMineBomb(item)) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cYou must be holding a refillable mine bomb.");
            return true;
        }

        MineBombItem.ParsedRefillableBomb parsed = MineBombItem.parseRefillable(item);
        if (parsed == null) {
            sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid refillable mine bomb data.");
            return true;
        }

        sender.sendMessage("§6§lREFILLABLE MINE BOMB INFO:");
        sender.sendMessage("§7Type: §e" + parsed.refillableType.getDisplayName());
        sender.sendMessage("§7Tier: §e" + parsed.tier.getLevel());
        sender.sendMessage("§7Cooldown: §e" + CooldownUtils.formatCooldownDuration((int) CooldownUtils.millisToMinutes(parsed.cooldownMillis)));
        
        if (parsed.isOnCooldown()) {
            long remaining = parsed.getRemainingCooldown();
            sender.sendMessage("§7Status: §cOn cooldown (§f" + CooldownUtils.formatRemainingTime(remaining) + " §cremaining)");
        } else {
            sender.sendMessage("§7Status: §aReady to use!");
        }

        return true;
    }

    private void sendUsage(CommandSender sender) {
        sender.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §eCommands:");
        sender.sendMessage("§7/refillableminebomb give <player> <money|tokens> <tier> <cooldown>");
        sender.sendMessage("§7/refillableminebomb reset <player> §8- §7Reset cooldowns");
        sender.sendMessage("§7/refillableminebomb info §8- §7Show info about held item");
        sender.sendMessage("§7Cooldown format: 5m, 2h, 1d (minutes/hours/days)");
    }
}
