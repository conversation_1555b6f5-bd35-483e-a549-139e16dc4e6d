package it.masterzen.minebomb;

import de.tr7zw.nbtapi.NBT;
import de.tr7zw.nbtapi.iface.ReadableItemNBT;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.function.Function;

/**
 * Simple test class to verify refillable mine bomb functionality
 * This is for development testing only
 */
public class RefillableMineBombTest {
    
    /**
     * Test method to verify NBT API integration works correctly
     * This method can be called from a command for testing purposes
     */
    public static String testRefillableMineBomb() {
        try {
            // Test creating a refillable mine bomb
            ItemStack refillableBomb = MineBombItem.createRefillable(
                RefillableBombType.REFILLABLE_MONEY, 
                BombTier.T1, 
                60 // 1 hour cooldown
            );
            
            // Test detection
            if (!MineBombItem.isRefillableMineBomb(refillableBomb)) {
                return "ERROR: Failed to detect refillable mine bomb";
            }
            
            // Test parsing
            MineBombItem.ParsedRefillableBomb parsed = MineBombItem.parseRefillable(refillableBomb);
            if (parsed == null) {
                return "ERROR: Failed to parse refillable mine bomb";
            }
            
            // Test NBT data
            String typeData = NBT.get(refillableBomb, (Function<ReadableItemNBT, String>) nbt -> nbt.getString(RefillableMineBombKeys.REFILLABLE_TYPE));
            if (typeData == null || !typeData.equals("REFILLABLE_MONEY")) {
                return "ERROR: NBT data not stored correctly - type: " + typeData;
            }
            
            // Test cooldown functionality
            if (parsed.isOnCooldown()) {
                return "ERROR: New bomb should not be on cooldown";
            }
            
            // Test setting last use time
            ItemStack usedBomb = MineBombItem.setLastUseTime(refillableBomb, System.currentTimeMillis());
            MineBombItem.ParsedRefillableBomb parsedUsed = MineBombItem.parseRefillable(usedBomb);
            
            if (parsedUsed == null || !parsedUsed.isOnCooldown()) {
                return "ERROR: Bomb should be on cooldown after use";
            }
            
            // Test cooldown reset
            ItemStack resetBomb = MineBombItem.resetCooldown(usedBomb);
            MineBombItem.ParsedRefillableBomb parsedReset = MineBombItem.parseRefillable(resetBomb);
            
            if (parsedReset == null || parsedReset.isOnCooldown()) {
                return "ERROR: Bomb should not be on cooldown after reset";
            }
            
            return "SUCCESS: All refillable mine bomb tests passed!";
            
        } catch (Exception e) {
            return "ERROR: Exception during testing: " + e.getMessage() + " - " + e.getClass().getSimpleName();
        }
    }
    
    /**
     * Test method for cooldown utilities
     */
    public static String testCooldownUtils() {
        try {
            // Test time formatting
            String formatted = CooldownUtils.formatRemainingTime(3661000); // 1 hour, 1 minute, 1 second
            if (!formatted.contains("1h") || !formatted.contains("1m") || !formatted.contains("1s")) {
                return "ERROR: Time formatting failed: " + formatted;
            }
            
            // Test cooldown validation
            if (!CooldownUtils.isValidCooldown(60)) {
                return "ERROR: Valid cooldown rejected";
            }
            
            if (CooldownUtils.isValidCooldown(2000)) { // Over 24 hours
                return "ERROR: Invalid cooldown accepted";
            }
            
            // Test cooldown parsing
            int parsed = CooldownUtils.parseCooldownString("2h");
            if (parsed != 120) {
                return "ERROR: Cooldown parsing failed: " + parsed;
            }
            
            return "SUCCESS: All cooldown utility tests passed!";
            
        } catch (Exception e) {
            return "ERROR: Exception during cooldown testing: " + e.getMessage();
        }
    }
}
