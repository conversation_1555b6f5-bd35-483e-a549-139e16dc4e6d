package it.masterzen.minebomb;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.prestigemine.MineManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.inventory.ItemStack;

import java.lang.reflect.Method;

public class MineBombListener implements Listener {

    private final AlphaBlockBreak plugin;

    public MineBombListener(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onDrop(PlayerDropItemEvent event) {
        ItemStack is = event.getItemDrop().getItemStack();
        Player player = event.getPlayer();
        if (!MineBombItem.isMineBomb(is)) return;

        // Validate player is in their own mine region
        MineManager mine = null;
        try {
            mine = plugin.getMineSystem().getMineAtLocation(player.getLocation());
        } catch (Throwable ignored) {}

        if (mine == null || mine.getOwner() == null || !mine.getOwner().equals(player.getUniqueId())) {
            player.sendMessage("§6§lMINE BOMB §8»§7 §cYou can only use this in your own mine.");
            event.setCancelled(true);
            return;
        }

        // Check if it's a refillable mine bomb
        if (MineBombItem.isRefillableMineBomb(is)) {
            handleRefillableMineBomb(event, player, is, mine);
            return;
        }

        // Handle regular mine bomb (existing logic)
        handleRegularMineBomb(event, player, is, mine);
    }

    private void handleRefillableMineBomb(PlayerDropItemEvent event, Player player, ItemStack is, MineManager mine) {
        // Cancel the drop event
        event.getItemDrop().remove();
        // event.setCancelled(true);

        // Check cooldown
        if (!MineBombItem.canUseRefillable(is)) {
            event.setCancelled(true);
            long remainingCooldown = MineBombItem.getRemainingCooldown(is);
            String timeLeft = CooldownUtils.formatRemainingTime(remainingCooldown);
            player.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cYou can use this again in §f" + timeLeft);
            return;
        }

        // Parse the refillable bomb
        MineBombItem.ParsedRefillableBomb parsed = MineBombItem.parseRefillable(is);
        if (parsed == null) {
            player.sendMessage("§6§lREFILLABLE MINE BOMB §8»§7 §cInvalid refillable mine bomb data.");
            return;
        }

        // Apply the mine bomb effect
        applyMineBombEffect(player, parsed.refillableType.getRegularType(), parsed.tier, mine);

        // Set the last use time and update the item in player's inventory
        ItemStack updatedItem = MineBombItem.setLastUseTime(is.clone(), System.currentTimeMillis());
        player.getInventory().setItemInMainHand(updatedItem);

        // Play effects
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_FIREWORK_LAUNCH, 1f, 1.2f);
        player.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION_NORMAL, player.getLocation(), 15);
    }

    private void handleRegularMineBomb(PlayerDropItemEvent event, Player player, ItemStack is, MineManager mine) {
        // Cancel actual drop and consume one item
        ItemStack hand = player.getInventory().getItemInMainHand();
        if (hand != null && hand.isSimilar(is)) {
            int amt = hand.getAmount();
            if (amt <= 1) {
                player.getInventory().setItemInMainHand(null);
            } else {
                hand.setAmount(amt - 1);
            }
            event.setCancelled(true);
        } else {
            // If not in main hand, remove from cursor/drop stack
            event.getItemDrop().remove();
        }

        MineBombItem.ParsedBomb parsed = MineBombItem.parse(is);
        if (parsed == null) return;

        // Apply the mine bomb effect
        applyMineBombEffect(player, parsed.type, parsed.tier, mine);

        // Play effects
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_FIREWORK_LAUNCH, 1f, 1.2f);
        player.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION_NORMAL, player.getLocation(), 15);
    }

    /**
     * Applies the mine bomb effect (shared logic for regular and refillable bombs)
     */
    private void applyMineBombEffect(Player player, BombType type, BombTier tier, MineManager mine) {
        double blocks = Math.pow(mine.getCurrentSize(), 2D) * 36;
        if (blocks <= 0) {
            blocks = 1;
        }

        // Compute percent based on tier and size (very small and auto-scaling)
        double base = 0.0000001; // 0.0005%
        switch (tier) {
            case T2: base = 0.0000002; break; // 0.001%
            case T3: base = 0.0000003; break; // 0.002%
        }
        // Scale by mine blocks
        double percent = base * (Math.min(blocks, 2_000_000L));

        if (type == BombType.MONEY) {
            double bal = plugin.getEconomy().getBalance(player);
            double reward = Math.max(0, bal * percent);
            if (reward > 0) {
                plugin.getEconomy().depositPlayer(player, reward);
                plugin.getResume().addValue(player, "Money", reward);
                player.sendMessage("§6§lMINE BOMB §8»§7 §a+ " + plugin.newFormatNumber(reward, player) + " §7Money (§f" + plugin.newFormatNumber(percent * 100, player) + "%§7)");
            }
        } else {
            // Use pick value
            double tokens = plugin.getPickValue(player);
            double reward = Math.max(0, tokens * percent);
            if (reward > 0) {
                plugin.getTeAPI().addTokens(player, reward);
                plugin.getResume().addValue(player, "Tokens", reward);
                player.sendMessage("§6§lMINE BOMB §8»§7 §a+ " + plugin.newFormatNumber(reward, player) + " §7Tokens (§f" + plugin.newFormatNumber(percent * 100, player) + "%§7)");
            }
        }
    }

    private long getMineCurrentSizeSafe(MineManager mine) {
        try {
            // Try common method names reflectively to avoid hard dependency on API signature
            for (String m : new String[]{"getCurrentSize", "getCurrentBlocks", "getSize", "getBlocks"}) {
                Method method = mine.getClass().getMethod(m);
                Object res = method.invoke(mine);
                if (res instanceof Number) {
                    return ((Number) res).longValue();
                }
            }
        } catch (Throwable ignored) {}
        // Fallback: try to estimate via mineSystem if available (not reliable here); default minimal
        return 0L;
    }
}
