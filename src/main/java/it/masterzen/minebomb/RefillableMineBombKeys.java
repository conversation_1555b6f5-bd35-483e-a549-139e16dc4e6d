package it.masterzen.minebomb;

/**
 * Container class for NBT key constants used in refillable mine bomb system
 */
public class RefillableMineBombKeys {

    // Keys for NBTItem storage
    public static final String LAST_USE_TIME = "refillable_last_use";
    public static final String COOLDOWN_DURATION = "refillable_cooldown";
    public static final String REFILLABLE_TYPE = "refillable_type";
    public static final String REFILLABLE_TIER = "refillable_tier";
    public static final String IS_REFILLABLE = "is_refillable";

    /**
     * No initialization needed for NBTItem keys
     * @param plugin The plugin instance (unused)
     */
    public static void initialize(Object plugin) {
        // No initialization needed for NBTItem keys
    }

    /**
     * Always returns true since no initialization is needed
     * @return true
     */
    public static boolean isInitialized() {
        return true;
    }
}
