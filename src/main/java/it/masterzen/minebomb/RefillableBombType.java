package it.masterzen.minebomb;

/**
 * Enum representing different types of refillable mine bombs.
 * Refillable mine bombs have cooldowns instead of being consumed on use.
 */
public enum RefillableBombType {
    REFILLABLE_MONEY("Money", "§6"),
    REFILLABLE_TOKENS("Tokens", "§a");

    private final String displayName;
    private final String colorCode;

    RefillableBombType(String displayName, String colorCode) {
        this.displayName = displayName;
        this.colorCode = colorCode;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getColorCode() {
        return colorCode;
    }

    /**
     * Gets the corresponding regular BombType for this refillable type
     */
    public BombType getRegularType() {
        switch (this) {
            case REFILLABLE_MONEY:
                return BombType.MONEY;
            case REFILLABLE_TOKENS:
                return BombType.TOKENS;
            default:
                return BombType.MONEY;
        }
    }

    /**
     * Converts a string to RefillableBombType
     */
    public static RefillableBombType fromString(String s) {
        if (s == null) return null;
        switch (s.toLowerCase()) {
            case "money":
            case "refillable_money":
                return REFILLABLE_MONEY;
            case "token":
            case "tokens":
            case "refillable_tokens":
                return REFILLABLE_TOKENS;
            default:
                return null;
        }
    }

    /**
     * Gets RefillableBombType from regular BombType
     */
    public static RefillableBombType fromBombType(BombType bombType) {
        if (bombType == null) return null;
        switch (bombType) {
            case MONEY:
                return REFILLABLE_MONEY;
            case TOKENS:
                return REFILLABLE_TOKENS;
            default:
                return null;
        }
    }
}
