package it.masterzen.Dungeon;

import de.tr7zw.nbtapi.NBTItem;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * Manager class for creating and handling custom swords.
 * Handles the creation of the Prismatic Blade and its properties.
 */
public class CustomSwordManager {
    
    /**
     * Creates a custom Prismatic Blade sword
     * 
     * @return The custom sword ItemStack
     */
    public static ItemStack createPrismaticBlade() {
        ItemStack sword = new ItemStack(Material.DIAMOND_SWORD, 1);
        ItemMeta meta = sword.getItemMeta();
        
        // Set display name
        meta.setDisplayName("§f§lPRISMATIC §6§lBLADE");
        
        // Create lore
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA legendary blade forged from the");
        lore.add("§7| §fessence of all elements. Its edge");
        lore.add("§7| §fshimmers with prismatic light and");
        lore.add("§7| §fcuts through reality itself.");
        lore.add("");
        lore.add("§e§lPROPERTIES");
        lore.add("§e| §fSharpness: §6XXX §7(Level 30)");
        lore.add("§e| §fDurability: §a∞ §7(Unbreakable)");
        lore.add("§e| §fRarity: §d§lLEGENDARY");
        lore.add("");
        lore.add("§6§lSPECIAL ABILITIES");
        lore.add("§6| §fForged from Prismatic Cores");
        lore.add("§6| §fChannels elemental power");
        lore.add("§6| §fNever breaks or dulls");
        lore.add("");
        lore.add("§d§lCRAFTED WITH ANCIENT MAGIC");
        lore.add("");
        
        meta.setLore(lore);
        
        // Make it unbreakable
        meta.setUnbreakable(true);
        
        // Add enchantments
        meta.addEnchant(Enchantment.DAMAGE_ALL, 30, true); // Sharpness 30
        
        sword.setItemMeta(meta);
        
        // Add NBT data to identify it as a custom sword
        NBTItem nbtItem = new NBTItem(sword);
        nbtItem.setString("custom_sword_type", "prismatic_blade");
        nbtItem.setString("custom_item_id", "prismatic_blade");
        nbtItem.setBoolean("is_custom_sword", true);
        
        // Add hidden lore for identification
        ItemStack finalSword = nbtItem.getItem();
        ItemMeta finalMeta = finalSword.getItemMeta();
        List<String> finalLore = new ArrayList<>(finalMeta.getLore());
        finalLore.add("§8§l§k||§r §8Custom Sword Data: prismatic_blade §8§l§k||");
        finalMeta.setLore(finalLore);
        finalSword.setItemMeta(finalMeta);
        
        return finalSword;
    }
    
    /**
     * Checks if an ItemStack is a custom sword
     * 
     * @param item The item to check
     * @return true if it's a custom sword
     */
    public static boolean isCustomSword(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return false;
        
        NBTItem nbtItem = new NBTItem(item);
        return nbtItem.hasKey("is_custom_sword") && nbtItem.getBoolean("is_custom_sword");
    }
    
    /**
     * Gets the custom sword type from an ItemStack
     * 
     * @param item The item to check
     * @return The custom sword type, or null if not a custom sword
     */
    public static String getCustomSwordType(ItemStack item) {
        if (!isCustomSword(item)) return null;
        
        NBTItem nbtItem = new NBTItem(item);
        return nbtItem.getString("custom_sword_type");
    }
    
    /**
     * Checks if an ItemStack is a Prismatic Blade
     * 
     * @param item The item to check
     * @return true if it's a Prismatic Blade
     */
    public static boolean isPrismaticBlade(ItemStack item) {
        String swordType = getCustomSwordType(item);
        return "prismatic_blade".equals(swordType);
    }
}
