package it.masterzen.Dungeon;

import it.masterzen.minebomb.BombTier;
import it.masterzen.minebomb.MineBombItem;
import it.masterzen.minebomb.RefillableBombType;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class for extracting all available custom crafting recipes from the CustomCraftingSystem.
 * Converts the internal recipe logic into displayable RecipeData objects for the Recipe Viewer GUI.
 */
public class RecipeExtractor {

    /**
     * Extracts all available custom crafting recipes
     *
     * @return List of all available recipes as RecipeData objects
     */
    public static List<RecipeData> extractAllRecipes() {
        List<RecipeData> recipes = new ArrayList<>();

        try {
            // Extract base bomb recipes
            List<RecipeData> baseBombRecipes = extractBaseBombRecipes();
            if (baseBombRecipes != null && !baseBombRecipes.isEmpty()) {
                recipes.addAll(baseBombRecipes);
            } else {
                System.err.println("[RecipeExtractor] Warning: No base bomb recipes extracted");
            }

            // Extract cooldown reduction recipes
            List<RecipeData> cooldownRecipes = extractCooldownReductionRecipes();
            if (cooldownRecipes != null && !cooldownRecipes.isEmpty()) {
                recipes.addAll(cooldownRecipes);
            } else {
                System.err.println("[RecipeExtractor] Warning: No cooldown reduction recipes extracted");
            }

            // Extract tier upgrade recipes
            List<RecipeData> tierUpgradeRecipes = extractTierUpgradeRecipes();
            if (tierUpgradeRecipes != null && !tierUpgradeRecipes.isEmpty()) {
                recipes.addAll(tierUpgradeRecipes);
            } else {
                System.err.println("[RecipeExtractor] Warning: No tier upgrade recipes extracted");
            }

            // Extract pet egg recipe
            RecipeData petEggRecipe = extractPetEggRecipe();
            if (petEggRecipe != null) {
                recipes.add(petEggRecipe);
            } else {
                System.err.println("[RecipeExtractor] Warning: Pet egg recipe extraction failed");
            }

            // Extract beetroot seed recipe
            RecipeData beetrootRecipe = extractBeetrootSeedRecipe();
            if (beetrootRecipe != null) {
                recipes.add(beetrootRecipe);
            } else {
                System.err.println("[RecipeExtractor] Warning: Beetroot seed recipe extraction failed");
            }

            // Extract discount skill recipe
            RecipeData discountRecipe = extractDiscountSkillRecipe();
            if (discountRecipe != null) {
                recipes.add(discountRecipe);
            } else {
                System.err.println("[RecipeExtractor] Warning: Discount skill recipe extraction failed");
            }

            // Extract ore upgrade recipes
            List<RecipeData> oreUpgradeRecipes = extractOreUpgradeRecipes();
            if (oreUpgradeRecipes != null && !oreUpgradeRecipes.isEmpty()) {
                recipes.addAll(oreUpgradeRecipes);
            } else {
                System.err.println("[RecipeExtractor] Warning: No ore upgrade recipes extracted");
            }

            // Extract custom sword recipe
            RecipeData customSwordRecipe = extractCustomSwordRecipe();
            if (customSwordRecipe != null) {
                recipes.add(customSwordRecipe);
            } else {
                System.err.println("[RecipeExtractor] Warning: Custom sword recipe extraction failed");
            }

            System.out.println("[RecipeExtractor] Successfully extracted " + recipes.size() + " recipes");

        } catch (Exception e) {
            System.err.println("[RecipeExtractor] Critical error during recipe extraction: " + e.getMessage());
            e.printStackTrace();

            // Return empty list instead of null to prevent NullPointerException
            return new ArrayList<>();
        }

        return recipes;
    }

    /**
     * Extracts base bomb creation recipes
     */
    private static List<RecipeData> extractBaseBombRecipes() {
        List<RecipeData> recipes = new ArrayList<>();

        try {

        // Money Bomb Recipe (4 Energium + pattern)
        ItemStack[] moneyBombIngredients = new ItemStack[9];
        moneyBombIngredients[1] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Top
        moneyBombIngredients[3] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Left
        moneyBombIngredients[4] = null; // Center (empty)
        moneyBombIngredients[5] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Right
        moneyBombIngredients[7] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Bottom

        ItemStack moneyBombResult = MineBombItem.createRefillable(RefillableBombType.REFILLABLE_MONEY, BombTier.T1, 30);

        recipes.add(new RecipeData(
            "Refillable Money Bomb T1",
            "Creates a Tier 1 Refillable Money Bomb with 30-minute cooldown. Provides money rewards based on your mine size and current money.",
            RecipeData.RecipeType.BASE_BOMB,
            RecipeData.RecipePattern.PLUS_PATTERN,
            moneyBombIngredients,
            moneyBombResult,
            Arrays.asList("Requires 4x Energium Crystal", "Uses Plus Pattern (+)"),
            Arrays.asList("Drop in your mine to activate", "Reusable with cooldown", "Reward scales with mine size")
        ));

        // Token Bomb Recipe (8 Energium furnace pattern)
        ItemStack[] tokenBombIngredients = new ItemStack[9];
        tokenBombIngredients[0] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[1] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[2] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[3] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[4] = null; // Center (empty)
        tokenBombIngredients[5] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[6] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[7] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);
        tokenBombIngredients[8] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1);

        ItemStack tokenBombResult = MineBombItem.createRefillable(RefillableBombType.REFILLABLE_TOKENS, BombTier.T1, 30);

        recipes.add(new RecipeData(
            "Refillable Token Bomb T1",
            "Creates a Tier 1 Refillable Token Bomb with 30-minute cooldown. Provides token rewards based on your mine size and pickaxe value.",
            RecipeData.RecipeType.BASE_BOMB,
            RecipeData.RecipePattern.FURNACE_PATTERN,
            tokenBombIngredients,
            tokenBombResult,
            Arrays.asList("Requires 8x Energium Crystal", "Uses Furnace Pattern (8 around center)"),
            Arrays.asList("Drop in your mine to activate", "Reusable with cooldown", "Reward scales with pickaxe value")
        ));

        } catch (Exception e) {
            System.err.println("[RecipeExtractor] Error extracting base bomb recipes: " + e.getMessage());
            e.printStackTrace();
        }

        return recipes;
    }

    /**
     * Extracts cooldown reduction recipes
     */
    private static List<RecipeData> extractCooldownReductionRecipes() {
        List<RecipeData> recipes = new ArrayList<>();

        // Money Bomb Cooldown Reduction (Money Bomb + 4 Temporium)
        ItemStack[] moneyReductionIngredients = new ItemStack[9];
        moneyReductionIngredients[1] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Top
        moneyReductionIngredients[3] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Left
        moneyReductionIngredients[4] = createPlaceholderBomb("Money Bomb", "§6§lRefillable Money Bomb §7(Any Tier)"); // Center
        moneyReductionIngredients[5] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Right
        moneyReductionIngredients[7] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Bottom

        ItemStack moneyReductionResult = createPlaceholderBomb("Improved Money Bomb", "§6§lRefillable Money Bomb §7(Cooldown -1 min)");

        recipes.add(new RecipeData(
            "Money Bomb Cooldown Reduction",
            "Reduces the cooldown of a Refillable Money Bomb by 1 minute (minimum 5 minutes). Place your existing bomb in the center.",
            RecipeData.RecipeType.COOLDOWN_REDUCTION,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            moneyReductionIngredients,
            moneyReductionResult,
            Arrays.asList("Requires existing Refillable Money Bomb", "Requires 4x Temporium Shard", "Minimum cooldown: 5 minutes"),
            Arrays.asList("Place your bomb in center slot", "Reduces cooldown by 1 minute", "Keeps same tier and type")
        ));

        // Token Bomb Cooldown Reduction (Token Bomb + 4 Mystrium)
        ItemStack[] tokenReductionIngredients = new ItemStack[9];
        tokenReductionIngredients[1] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Top
        tokenReductionIngredients[3] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Left
        tokenReductionIngredients[4] = createPlaceholderBomb("Token Bomb", "§6§lRefillable Token Bomb §7(Any Tier)"); // Center
        tokenReductionIngredients[5] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Right
        tokenReductionIngredients[7] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Bottom

        ItemStack tokenReductionResult = createPlaceholderBomb("Improved Token Bomb", "§6§lRefillable Token Bomb §7(Cooldown -1 min)");

        recipes.add(new RecipeData(
            "Token Bomb Cooldown Reduction",
            "Reduces the cooldown of a Refillable Token Bomb by 1 minute (minimum 5 minutes). Place your existing bomb in the center.",
            RecipeData.RecipeType.COOLDOWN_REDUCTION,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            tokenReductionIngredients,
            tokenReductionResult,
            Arrays.asList("Requires existing Refillable Token Bomb", "Requires 4x Mystrium Essence", "Minimum cooldown: 5 minutes"),
            Arrays.asList("Place your bomb in center slot", "Reduces cooldown by 1 minute", "Keeps same tier and type")
        ));

        return recipes;
    }

    /**
     * Extracts tier upgrade recipes
     */
    private static List<RecipeData> extractTierUpgradeRecipes() {
        List<RecipeData> recipes = new ArrayList<>();

        // T1 to T2 Upgrade (Any Bomb + 4 Voidstone)
        ItemStack[] t1ToT2Ingredients = new ItemStack[9];
        t1ToT2Ingredients[1] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Top
        t1ToT2Ingredients[3] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Left
        t1ToT2Ingredients[4] = createPlaceholderBomb("T1 Bomb", "§6§lRefillable Bomb §8T1 §7(Any Type)"); // Center
        t1ToT2Ingredients[5] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Right
        t1ToT2Ingredients[7] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Bottom

        ItemStack t1ToT2Result = createPlaceholderBomb("T2 Bomb", "§6§lRefillable Bomb §8T2 §7(Same Type)");

        recipes.add(new RecipeData(
            "Tier 1 → Tier 2 Upgrade",
            "Upgrades any Tier 1 Refillable Bomb to Tier 2. Keeps the same cooldown and bomb type (Money/Token).",
            RecipeData.RecipeType.TIER_UPGRADE,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            t1ToT2Ingredients,
            t1ToT2Result,
            Arrays.asList("Requires T1 Refillable Bomb", "Requires 4x Voidstone Fragment", "Works with any bomb type"),
            Arrays.asList("Place T1 bomb in center slot", "Upgrades to T2 tier", "Keeps cooldown and type")
        ));

        // T2 to T3 Upgrade (Any Bomb + 4 Prismatic)
        ItemStack[] t2ToT3Ingredients = new ItemStack[9];
        t2ToT3Ingredients[1] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Top
        t2ToT3Ingredients[3] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Left
        t2ToT3Ingredients[4] = createPlaceholderBomb("T2 Bomb", "§6§lRefillable Bomb §8T2 §7(Any Type)"); // Center
        t2ToT3Ingredients[5] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Right
        t2ToT3Ingredients[7] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Bottom

        ItemStack t2ToT3Result = createPlaceholderBomb("T3 Bomb", "§6§lRefillable Bomb §8T3 §7(Same Type)");

        recipes.add(new RecipeData(
            "Tier 2 → Tier 3 Upgrade",
            "Upgrades any Tier 2 Refillable Bomb to Tier 3. Keeps the same cooldown and bomb type (Money/Token).",
            RecipeData.RecipeType.TIER_UPGRADE,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            t2ToT3Ingredients,
            t2ToT3Result,
            Arrays.asList("Requires T2 Refillable Bomb", "Requires 4x Prismatic Core", "Works with any bomb type"),
            Arrays.asList("Place T2 bomb in center slot", "Upgrades to T3 tier", "Keeps cooldown and type")
        ));

        return recipes;
    }

    /**
     * Extracts pet egg creation recipe
     */
    private static RecipeData extractPetEggRecipe() {
        // Pet Egg Recipe (8 Prismatic cores furnace pattern)
        ItemStack[] petEggIngredients = new ItemStack[9];
        petEggIngredients[0] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[1] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[2] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[3] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[4] = null; // Center (empty)
        petEggIngredients[5] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[6] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[7] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);
        petEggIngredients[8] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);

        // Create pet egg result (using the method from CustomCraftingSystem)
        ItemStack petEggResult = createPetEgg();

        return new RecipeData(
            "Pet Egg",
            "Creates a mysterious Pet Egg that hatches into a random pet after breaking 35,000 blocks while holding it in your offhand.",
            RecipeData.RecipeType.PET_EGG,
            RecipeData.RecipePattern.FURNACE_PATTERN,
            petEggIngredients,
            petEggResult,
            Arrays.asList("Requires 8x Prismatic Core", "Uses Furnace Pattern (8 around center)"),
            Arrays.asList("Hold in offhand while mining", "Hatches after 35,000 blocks", "Gives random pet")
        );
    }

    /**
     * Extracts beetroot seed creation recipe
     */
    private static RecipeData extractBeetrootSeedRecipe() {
        // Beetroot Seed Recipe (4 Prismatic + custom carrot seed in center)
        ItemStack[] beetrootIngredients = new ItemStack[9];
        beetrootIngredients[1] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Top
        beetrootIngredients[3] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Left
        beetrootIngredients[4] = createCustomCarrotSeed(); // Center
        beetrootIngredients[5] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Right
        beetrootIngredients[7] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Bottom

        ItemStack beetrootResult = createCustomBeetrootSeed();

        return new RecipeData(
            "Custom Beetroot Seed",
            "Creates a Custom Beetroot Seed that gives rewards from all three seed types when harvested. Auto-replants when fully grown.",
            RecipeData.RecipeType.BEETROOT_SEED,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            beetrootIngredients,
            beetrootResult,
            Arrays.asList("Requires Custom Carrot Seed", "Requires 4x Prismatic Core", "Uses Plus Pattern with center"),
            Arrays.asList("Plant on your island", "Gives all seed rewards", "Auto-replants when grown")
        );
    }

    /**
     * Extracts discount skill book creation recipe
     */
    private static RecipeData extractDiscountSkillRecipe() {
        // Discount Skill Recipe (4 Prismatic + standard book in center)
        ItemStack[] discountIngredients = new ItemStack[9];
        discountIngredients[1] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Top
        discountIngredients[3] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Left
        discountIngredients[4] = new ItemStack(Material.BOOK, 1); // Center
        discountIngredients[5] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Right
        discountIngredients[7] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Bottom

        ItemStack discountResult = createDiscountSkillBook();

        return new RecipeData(
            "Discount Skill Book",
            "Creates a Discount Skill Book that provides +1% enchant discount when used. Right-click to claim permanently.",
            RecipeData.RecipeType.DISCOUNT_SKILL,
            RecipeData.RecipePattern.PLUS_WITH_CENTER,
            discountIngredients,
            discountResult,
            Arrays.asList("Requires standard Book", "Requires 4x Prismatic Core", "Uses Plus Pattern with center"),
            Arrays.asList("Right-click to use", "Gives +1% enchant discount", "Maximum 10% total discount")
        );
    }

    /**
     * Creates a placeholder bomb item for display purposes
     */
    private static ItemStack createPlaceholderBomb(String name, String displayName) {
        ItemStack placeholder = new ItemStack(Material.FIREWORK_CHARGE, 1);
        ItemMeta meta = placeholder.getItemMeta();
        meta.setDisplayName(displayName);

        List<String> lore = new ArrayList<>();
        lore.add("§7§oPlace your existing bomb here");
        lore.add("§7§oThis is just a placeholder for display");
        meta.setLore(lore);

        placeholder.setItemMeta(meta);
        return placeholder;
    }

    /**
     * Creates a pet egg item (copied from CustomCraftingSystem)
     */
    private static ItemStack createPetEgg() {
        ItemStack petEgg = new ItemStack(Material.MONSTER_EGG, 1, (short) 98); // Ocelot spawn egg
        ItemMeta meta = petEgg.getItemMeta();
        meta.setDisplayName("§6§lPET EGG");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mysterious egg that will hatch");
        lore.add("§7| §finto a random pet after breaking");
        lore.add("§7| §e35,000 blocks §fwhile holding it!");
        lore.add("");
        lore.add("§e§lPROGRESS");
        lore.add("§e| §fBlocks Broken: §a0§f/§e35,000");
        lore.add("§e| §fRemaining: §c35,000 blocks");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fHold this egg in offhand while mining");
        lore.add("§6| §fto progress towards hatching!");
        lore.add("");

        meta.setLore(lore);
        petEgg.setItemMeta(meta);
        return petEgg;
    }

    /**
     * Creates a custom carrot seed item
     */
    private static ItemStack createCustomCarrotSeed() {
        ItemStack seed = new ItemStack(Material.CARROT, 1);
        ItemMeta meta = seed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM CARROT §7Seed");

        List<String> lore = new ArrayList<>();
        lore.add("§7§oRequired ingredient for Custom Beetroot Seed");
        meta.setLore(lore);

        seed.setItemMeta(meta);
        return seed;
    }

    /**
     * Creates a custom beetroot seed item (copied from CustomCraftingSystem)
     */
    private static ItemStack createCustomBeetrootSeed() {
        ItemStack seed = new ItemStack(Material.BEETROOT_SEEDS, 1);
        ItemMeta meta = seed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM BEETROOT §7Seed");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fPlant on your island to grow");
        lore.add("§7| §fcustom beetroot that gives");
        lore.add("§7| §a§lALL THREE SEED REWARDS §7when harvested!");
        lore.add("");
        lore.add("§a§lREWARDS WHEN HARVESTED");
        lore.add("§a| §f5-15 Fortune levels §7(from Wheat)");
        lore.add("§a| §f5-10 Token Greed levels §7(from Carrot)");
        lore.add("§a| §f5% Money OR Token Boost §7(from Potato)");
        lore.add("§a| §6§lAUTO-REPLANTS §7when fully grown!");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fPlant on farmland on your island");
        lore.add("§6| §fWait for it to grow, then harvest!");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        lore.add("");

        meta.setLore(lore);
        seed.setItemMeta(meta);
        return seed;
    }

    /**
     * Creates a discount skill book item (copied from CustomCraftingSystem)
     */
    private static ItemStack createDiscountSkillBook() {
        ItemStack book = new ItemStack(Material.BOOK, 1);
        ItemMeta meta = book.getItemMeta();
        meta.setDisplayName("§6§lDISCOUNT SKILL §7Book");

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mystical book that teaches");
        lore.add("§7| §fthe art of enchantment bargaining!");
        lore.add("§7| §eRight-click §fto claim §a+1% enchant discount");
        lore.add("");
        lore.add("§a§lBENEFITS");
        lore.add("§a| §f+1% discount on ALL enchantments");
        lore.add("§a| §fStacks with other discounts");
        lore.add("§a| §fMaximum: 10% total discount");
        lore.add("");
        lore.add("§e§lHOW IT WORKS");
        lore.add("§e| §fReduces the cost of enchanting");
        lore.add("§e| §fyour pickaxe with any enchantment");
        lore.add("§e| §fApplies to Tokens, Crystals, and Beacon Points");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fRight-click this book to claim");
        lore.add("§6| §fthe discount permanently!");
        lore.add("");
        lore.add("§c§lNOTE: §7This book will be consumed on use");
        lore.add("");

        meta.setLore(lore);
        book.setItemMeta(meta);
        return book;
    }

    /**
     * Extracts ore upgrade recipes (4 of one ore type to get 1 of the next tier)
     */
    private static List<RecipeData> extractOreUpgradeRecipes() {
        List<RecipeData> recipes = new ArrayList<>();

        try {
            // ENERGIUM -> TEMPORIUM
            ItemStack[] energiumIngredients = new ItemStack[9];
            energiumIngredients[1] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Top
            energiumIngredients[3] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Left
            energiumIngredients[4] = null; // Center (empty)
            energiumIngredients[5] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Right
            energiumIngredients[7] = CustomOreManager.createCustomOre(CustomOre.ENERGIUM, 1); // Bottom

            ItemStack temporiumResult = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1);

            recipes.add(new RecipeData(
                "Energium → Temporium Upgrade",
                "Upgrade 4 Energium Crystals into 1 Temporium Shard. The temporal energy condenses the raw elemental power into a more refined form.",
                RecipeData.RecipeType.ORE_UPGRADE,
                RecipeData.RecipePattern.PLUS_PATTERN,
                energiumIngredients,
                temporiumResult,
                Arrays.asList("Requires 4x Energium Crystal", "Uses Plus Pattern (+)", "Empty center required"),
                Arrays.asList("Higher tier ore", "More valuable", "Used in advanced recipes")
            ));

            // TEMPORIUM -> MYSTRIUM
            ItemStack[] temporiumIngredients = new ItemStack[9];
            temporiumIngredients[1] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Top
            temporiumIngredients[3] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Left
            temporiumIngredients[4] = null; // Center (empty)
            temporiumIngredients[5] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Right
            temporiumIngredients[7] = CustomOreManager.createCustomOre(CustomOre.TEMPORIUM, 1); // Bottom

            ItemStack mystriumResult = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1);

            recipes.add(new RecipeData(
                "Temporium → Mystrium Upgrade",
                "Upgrade 4 Temporium Shards into 1 Mystrium Essence. The temporal fragments merge into pure mystical energy.",
                RecipeData.RecipeType.ORE_UPGRADE,
                RecipeData.RecipePattern.PLUS_PATTERN,
                temporiumIngredients,
                mystriumResult,
                Arrays.asList("Requires 4x Temporium Shard", "Uses Plus Pattern (+)", "Empty center required"),
                Arrays.asList("Higher tier ore", "More valuable", "Used in legendary recipes")
            ));

            // MYSTRIUM -> VOIDSTONE
            ItemStack[] mystriumIngredients = new ItemStack[9];
            mystriumIngredients[1] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Top
            mystriumIngredients[3] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Left
            mystriumIngredients[4] = null; // Center (empty)
            mystriumIngredients[5] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Right
            mystriumIngredients[7] = CustomOreManager.createCustomOre(CustomOre.MYSTRIUM, 1); // Bottom

            ItemStack voidstoneResult = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1);

            recipes.add(new RecipeData(
                "Mystrium → Voidstone Upgrade",
                "Upgrade 4 Mystrium Essences into 1 Voidstone Fragment. The mystical energy condenses into void matter.",
                RecipeData.RecipeType.ORE_UPGRADE,
                RecipeData.RecipePattern.PLUS_PATTERN,
                mystriumIngredients,
                voidstoneResult,
                Arrays.asList("Requires 4x Mystrium Essence", "Uses Plus Pattern (+)", "Empty center required"),
                Arrays.asList("Higher tier ore", "More valuable", "Used in mythical recipes")
            ));

            // VOIDSTONE -> PRISMATIC
            ItemStack[] voidstoneIngredients = new ItemStack[9];
            voidstoneIngredients[1] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Top
            voidstoneIngredients[3] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Left
            voidstoneIngredients[4] = null; // Center (empty)
            voidstoneIngredients[5] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Right
            voidstoneIngredients[7] = CustomOreManager.createCustomOre(CustomOre.VOIDSTONE, 1); // Bottom

            ItemStack prismaticResult = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1);

            recipes.add(new RecipeData(
                "Voidstone → Prismatic Upgrade",
                "Upgrade 4 Voidstone Fragments into 1 Prismatic Core. The void energy crystallizes into the ultimate material.",
                RecipeData.RecipeType.ORE_UPGRADE,
                RecipeData.RecipePattern.PLUS_PATTERN,
                voidstoneIngredients,
                prismaticResult,
                Arrays.asList("Requires 4x Voidstone Fragment", "Uses Plus Pattern (+)", "Empty center required"),
                Arrays.asList("Ultimate tier ore", "Most valuable", "Used in ascended recipes")
            ));

        } catch (Exception e) {
            System.err.println("[RecipeExtractor] Error extracting ore upgrade recipes: " + e.getMessage());
            e.printStackTrace();
        }

        return recipes;
    }

    /**
     * Extracts custom sword recipe (stick + 2 prismatic cores in sword pattern)
     */
    private static RecipeData extractCustomSwordRecipe() {
        try {
            // Sword pattern: top center (1), middle center (4), bottom center (7)
            ItemStack[] swordIngredients = new ItemStack[9];
            swordIngredients[1] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Top
            swordIngredients[4] = CustomOreManager.createCustomOre(CustomOre.PRISMATIC, 1); // Middle
            swordIngredients[7] = new ItemStack(Material.STICK, 1); // Bottom (handle)

            ItemStack swordResult = CustomSwordManager.createPrismaticBlade();

            return new RecipeData(
                "Prismatic Blade",
                "Forge the legendary Prismatic Blade using 2 Prismatic Cores and a stick. This unbreakable sword channels the power of all elements with Sharpness 30.",
                RecipeData.RecipeType.CUSTOM_SWORD,
                RecipeData.RecipePattern.SWORD_PATTERN,
                swordIngredients,
                swordResult,
                Arrays.asList("Requires 2x Prismatic Core", "Requires 1x Stick", "Uses Sword Pattern (vertical line)"),
                Arrays.asList("Unbreakable weapon", "Sharpness XXX (Level 30)", "Legendary rarity", "Channels elemental power")
            );

        } catch (Exception e) {
            System.err.println("[RecipeExtractor] Error extracting custom sword recipe: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}