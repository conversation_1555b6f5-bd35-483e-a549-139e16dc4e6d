package it.masterzen.Dungeon;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * GUI class for displaying custom crafting recipes in a crafting table format.
 * Shows one recipe per page with navigation support and proper ingredient positioning.
 */
public class RecipeViewerGUI implements Listener {

    private final AlphaBlockBreak plugin;
    private final String prefix = "§e§lRECIPES §8»§7 ";

    // Track which page each player is viewing
    private final Map<UUID, Integer> playerPages = new HashMap<>();

    // Cache recipes to avoid repeated extraction
    private List<RecipeData> allRecipes;

    // GUI layout constants
    private static final int GUI_SIZE = 54; // 6 rows
    private static final int[] CRAFTING_SLOTS = {10, 11, 12, 19, 20, 21, 28, 29, 30}; // 3x3 crafting grid
    private static final int RESULT_SLOT = 24; // Result display slot
    private static final int INFO_SLOT = 4; // Recipe info slot
    private static final int PREV_PAGE_SLOT = 45; // Previous page button
    private static final int NEXT_PAGE_SLOT = 53; // Next page button
    private static final int CLOSE_SLOT = 49; // Close button

    public RecipeViewerGUI(AlphaBlockBreak plugin) {
        this.plugin = plugin;
        loadRecipes();
    }

    /**
     * Loads all recipes from the RecipeExtractor
     */
    private void loadRecipes() {
        try {
            allRecipes = RecipeExtractor.extractAllRecipes();
            plugin.getLogger().info("Loaded " + allRecipes.size() + " custom crafting recipes");
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to load custom crafting recipes: " + e.getMessage());
            e.printStackTrace();
            allRecipes = new ArrayList<>();
        }
    }

    /**
     * Opens the recipe viewer GUI for a player
     *
     * @param player The player to open the GUI for
     */
    public void openGUI(Player player) {
        openGUI(player, 0);
    }

    /**
     * Opens the recipe viewer GUI for a player at a specific page
     *
     * @param player The player to open the GUI for
     * @param page The page number to display (0-based)
     */
    public void openGUI(Player player, int page) {
        try {
            if (allRecipes.isEmpty()) {
                player.sendMessage(prefix + "§cNo custom recipes are currently available!");
                player.sendMessage(prefix + "§7This might be due to a configuration issue. Please contact an administrator.");
                return;
            }

            // Validate page number
            int maxPage = allRecipes.size() - 1;
            if (page < 0) {
                page = 0;
                player.sendMessage(prefix + "§7Showing first page instead.");
            }
            if (page > maxPage) {
                page = maxPage;
                player.sendMessage(prefix + "§7Showing last page instead.");
            }

            // Store player's current page
            playerPages.put(player.getUniqueId(), page);

            // Create GUI
            String title = "§e§lRECIPES §f| §7Page " + (page + 1) + "/" + (maxPage + 1);
            Inventory gui = Bukkit.createInventory(null, GUI_SIZE, title);

            // Fill border with error handling
            try {
                it.masterzen.commands.Main.FillBorder(gui);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to fill GUI border: " + e.getMessage());
                // Continue without border
            }

            // Get current recipe
            RecipeData recipe = allRecipes.get(page);
            if (recipe == null) {
                player.sendMessage(prefix + "§cError: Recipe data is corrupted for page " + (page + 1));
                return;
            }

            // Set up GUI components with error handling
            try {
                setupCraftingGrid(gui, recipe);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to setup crafting grid: " + e.getMessage());
                player.sendMessage(prefix + "§cWarning: Recipe display may be incomplete.");
            }

            try {
                setupResultSlot(gui, recipe);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to setup result slot: " + e.getMessage());
            }

            try {
                setupInfoSlot(gui, recipe);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to setup info slot: " + e.getMessage());
            }

            try {
                setupNavigationButtons(gui, page, maxPage);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to setup navigation buttons: " + e.getMessage());
            }

            // Open GUI for player
            player.openInventory(gui);
            player.sendMessage(prefix + "§aShowing recipe: §f" + recipe.getRecipeName());

        } catch (Exception e) {
            player.sendMessage(prefix + "§cFailed to open recipe viewer: " + e.getMessage());
            player.sendMessage(prefix + "§7Please try again or contact an administrator if the problem persists.");
            plugin.getLogger().severe("Critical error opening recipe GUI for " + player.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sets up the 3x3 crafting grid with recipe ingredients
     */
    private void setupCraftingGrid(Inventory gui, RecipeData recipe) {
        ItemStack[] ingredients = recipe.getIngredients();

        for (int i = 0; i < 9; i++) {
            ItemStack ingredient = ingredients[i];
            int guiSlot = CRAFTING_SLOTS[i];

            if (ingredient != null) {
                // Clone the ingredient and add visual indicators
                ItemStack displayItem = ingredient.clone();
                ItemMeta meta = displayItem.getItemMeta();

                if (meta != null) {
                    List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
                    lore.add("");
                    lore.add("§8§l» §7Required for this recipe");
                    meta.setLore(lore);
                    displayItem.setItemMeta(meta);
                }

                gui.setItem(guiSlot, displayItem);
            } else {
                // Empty slot - add placeholder
                ItemStack placeholder = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 8); // Light gray
                ItemMeta meta = placeholder.getItemMeta();
                meta.setDisplayName("§7Empty Slot");
                placeholder.setItemMeta(meta);
                gui.setItem(guiSlot, placeholder);
            }
        }
    }

    /**
     * Sets up the result slot with the recipe result
     */
    private void setupResultSlot(Inventory gui, RecipeData recipe) {
        ItemStack result = recipe.getResult();
        if (result != null) {
            ItemStack displayResult = result.clone();
            ItemMeta meta = displayResult.getItemMeta();

            if (meta != null) {
                List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
                lore.add("");
                lore.add("§a§l» §aResult of this recipe");
                meta.setLore(lore);
                displayResult.setItemMeta(meta);
            }

            gui.setItem(RESULT_SLOT, displayResult);
        }
    }

    /**
     * Sets up the info slot with recipe information
     */
    private void setupInfoSlot(Inventory gui, RecipeData recipe) {
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta meta = info.getItemMeta();
        meta.setDisplayName("§6§l" + recipe.getRecipeName());

        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lTYPE: §f" + recipe.getType().getDisplayName());
        lore.add("§e§lPATTERN: §f" + recipe.getPattern().getDisplayName());
        lore.add("");
        lore.add("§7§lDESCRIPTION:");

        // Split description into multiple lines if too long
        String description = recipe.getDescription();
        String[] words = description.split(" ");
        StringBuilder currentLine = new StringBuilder("§7| §f");

        for (String word : words) {
            if (currentLine.length() + word.length() + 1 > 45) {
                lore.add(currentLine.toString());
                currentLine = new StringBuilder("§7| §f" + word);
            } else {
                if (currentLine.length() > 7) currentLine.append(" ");
                currentLine.append(word);
            }
        }
        if (currentLine.length() > 7) {
            lore.add(currentLine.toString());
        }

        // Add requirements
        if (!recipe.getRequirements().isEmpty()) {
            lore.add("");
            lore.add("§c§lREQUIREMENTS:");
            for (String requirement : recipe.getRequirements()) {
                lore.add("§c| §f" + requirement);
            }
        }

        // Add notes
        if (!recipe.getNotes().isEmpty()) {
            lore.add("");
            lore.add("§a§lNOTES:");
            for (String note : recipe.getNotes()) {
                lore.add("§a| §f" + note);
            }
        }

        meta.setLore(lore);
        info.setItemMeta(meta);
        gui.setItem(INFO_SLOT, info);
    }

    /**
     * Sets up navigation buttons (previous, next, close)
     */
    private void setupNavigationButtons(Inventory gui, int currentPage, int maxPage) {
        // Previous page button
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta meta = prevButton.getItemMeta();
            meta.setDisplayName("§a§lPrevious Page");
            List<String> lore = new ArrayList<>();
            lore.add("§7Click to go to page " + currentPage);
            meta.setLore(lore);
            prevButton.setItemMeta(meta);
            gui.setItem(PREV_PAGE_SLOT, prevButton);
        }

        // Next page button
        if (currentPage < maxPage) {
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta meta = nextButton.getItemMeta();
            meta.setDisplayName("§a§lNext Page");
            List<String> lore = new ArrayList<>();
            lore.add("§7Click to go to page " + (currentPage + 2));
            meta.setLore(lore);
            nextButton.setItemMeta(meta);
            gui.setItem(NEXT_PAGE_SLOT, nextButton);
        }

        // Close button
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        ItemMeta meta = closeButton.getItemMeta();
        meta.setDisplayName("§c§lClose");
        List<String> lore = new ArrayList<>();
        lore.add("§7Click to close this menu");
        meta.setLore(lore);
        closeButton.setItemMeta(meta);
        gui.setItem(CLOSE_SLOT, closeButton);
    }

    /**
     * Handles inventory click events
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        // Check if this is our GUI
        if (!title.startsWith("§e§lRECIPES §f| §7Page")) return;

        event.setCancelled(true); // Cancel all clicks in our GUI

        if (event.getClickedInventory() == null ||
            !event.getClickedInventory().equals(event.getView().getTopInventory())) {
            return;
        }

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        int slot = event.getSlot();
        Integer currentPage = playerPages.get(player.getUniqueId());
        if (currentPage == null) currentPage = 0;

        // Handle navigation clicks
        if (slot == PREV_PAGE_SLOT && clickedItem.getType() == Material.ARROW) {
            if (currentPage > 0) {
                openGUI(player, currentPage - 1);
            }
        } else if (slot == NEXT_PAGE_SLOT && clickedItem.getType() == Material.ARROW) {
            int maxPage = allRecipes.size() - 1;
            if (currentPage < maxPage) {
                openGUI(player, currentPage + 1);
            }
        } else if (slot == CLOSE_SLOT && clickedItem.getType() == Material.BARRIER) {
            player.closeInventory();
            playerPages.remove(player.getUniqueId());
        }
    }

    /**
     * Cleans up player data when they close the inventory
     */
    public void onInventoryClose(Player player) {
        playerPages.remove(player.getUniqueId());
    }

    /**
     * Gets the total number of available recipes
     */
    public int getTotalRecipes() {
        return allRecipes.size();
    }

    /**
     * Reloads recipes from the extractor
     */
    public void reloadRecipes() {
        loadRecipes();
        // Clear all player pages to force refresh
        playerPages.clear();
    }
}