package it.masterzen.Dungeon;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.List;

/**
 * Data structure representing a custom crafting recipe for display in the Recipe Viewer GUI.
 * Contains all information needed to display a recipe including ingredients, result, and metadata.
 */
public class RecipeData {

    public enum RecipeType {
        BASE_BOMB("Base Bomb Creation"),
        COOLDOWN_REDUCTION("Cooldown Reduction"),
        TIER_UPGRADE("Tier Upgrade"),
        PET_EGG("Pet Egg Creation"),
        BEETROOT_SEED("Custom Beetroot Seed"),
        DISCOUNT_SKILL("Discount Skill Book"),
        ORE_UPGRADE("Ore Upgrade"),
        CUSTOM_SWORD("Custom Sword Creation");

        private final String displayName;

        RecipeType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum RecipePattern {
        PLUS_PATTERN("Plus Pattern (+)"),
        FURNACE_PATTERN("<PERSON>rna<PERSON> Pattern (8 around center)"),
        PLUS_WITH_CENTER("Plus Pattern with Center Item"),
        SWORD_PATTERN("Sword Pattern (vertical line)");

        private final String displayName;

        RecipePattern(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private final String recipeName;
    private final String description;
    private final RecipeType type;
    private final RecipePattern pattern;
    private final ItemStack[] ingredients; // 3x3 matrix (9 slots)
    private final ItemStack result;
    private final List<String> requirements;
    private final List<String> notes;

    /**
     * Creates a new RecipeData instance
     *
     * @param recipeName The display name of the recipe
     * @param description A detailed description of what the recipe does
     * @param type The type/category of the recipe
     * @param pattern The crafting pattern used
     * @param ingredients 3x3 matrix of ingredients (9 slots, null for empty slots)
     * @param result The resulting item from the recipe
     * @param requirements List of requirements or conditions for the recipe
     * @param notes Additional notes or tips about the recipe
     */
    public RecipeData(String recipeName, String description, RecipeType type, RecipePattern pattern,
                     ItemStack[] ingredients, ItemStack result, List<String> requirements, List<String> notes) {
        if (ingredients.length != 9) {
            throw new IllegalArgumentException("Ingredients array must be exactly 9 elements (3x3 grid)");
        }

        this.recipeName = recipeName;
        this.description = description;
        this.type = type;
        this.pattern = pattern;
        this.ingredients = ingredients.clone(); // Defensive copy
        this.result = result != null ? result.clone() : null;
        this.requirements = requirements;
        this.notes = notes;
    }

    // Getters
    public String getRecipeName() {
        return recipeName;
    }

    public String getDescription() {
        return description;
    }

    public RecipeType getType() {
        return type;
    }

    public RecipePattern getPattern() {
        return pattern;
    }

    public ItemStack[] getIngredients() {
        return ingredients.clone(); // Defensive copy
    }

    public ItemStack getIngredient(int slot) {
        if (slot < 0 || slot >= 9) {
            throw new IllegalArgumentException("Slot must be between 0 and 8");
        }
        return ingredients[slot] != null ? ingredients[slot].clone() : null;
    }

    public ItemStack getResult() {
        return result != null ? result.clone() : null;
    }

    public List<String> getRequirements() {
        return requirements;
    }

    public List<String> getNotes() {
        return notes;
    }

    /**
     * Converts a 2D coordinate to 1D array index for the 3x3 grid
     *
     * @param row Row (0-2)
     * @param col Column (0-2)
     * @return Array index (0-8)
     */
    public static int coordToIndex(int row, int col) {
        if (row < 0 || row > 2 || col < 0 || col > 2) {
            throw new IllegalArgumentException("Row and column must be between 0 and 2");
        }
        return row * 3 + col;
    }

    /**
     * Gets ingredient at specific row and column
     *
     * @param row Row (0-2)
     * @param col Column (0-2)
     * @return ItemStack at that position, or null if empty
     */
    public ItemStack getIngredient(int row, int col) {
        return getIngredient(coordToIndex(row, col));
    }

    /**
     * Checks if the recipe has any ingredients
     *
     * @return true if at least one ingredient slot is not null
     */
    public boolean hasIngredients() {
        for (ItemStack ingredient : ingredients) {
            if (ingredient != null && ingredient.getType() != Material.AIR) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets the number of non-empty ingredient slots
     *
     * @return Count of ingredient slots that are not null or air
     */
    public int getIngredientCount() {
        int count = 0;
        for (ItemStack ingredient : ingredients) {
            if (ingredient != null && ingredient.getType() != Material.AIR) {
                count++;
            }
        }
        return count;
    }

    @Override
    public String toString() {
        return "RecipeData{" +
                "recipeName='" + recipeName + '\'' +
                ", type=" + type +
                ", pattern=" + pattern +
                ", ingredientCount=" + getIngredientCount() +
                ", hasResult=" + (result != null) +
                '}';
    }
}