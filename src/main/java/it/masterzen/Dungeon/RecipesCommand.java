package it.masterzen.Dungeon;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Command executor for the /recipes command.
 * Opens the Recipe Viewer GUI to show all available custom crafting recipes.
 */
public class RecipesCommand implements CommandExecutor {

    private final AlphaBlockBreak plugin;
    private final RecipeViewerGUI gui;
    private final String prefix = "§e§lRECIPES §8»§7 ";

    public RecipesCommand(AlphaBlockBreak plugin) {
        this.plugin = plugin;
        this.gui = new RecipeViewerGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (!cmd.getName().equalsIgnoreCase("recipes")) {
            return false;
        }

        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(prefix + "§cThis command can only be used by players!");
            return true;
        }

        Player player = (Player) sender;

        // Handle different command arguments
        if (args.length == 0) {
            // Open GUI at first page
            openRecipeGUI(player, 0);
        } else if (args.length == 1) {
            // Handle subcommands
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "help":
                case "?":
                    sendHelpMessage(player);
                    break;

                case "reload":
                    if (player.hasPermission("alphablockbreak.recipes.reload") || player.isOp()) {
                        reloadRecipes(player);
                    } else {
                        player.sendMessage(prefix + "§cYou don't have permission to reload recipes!");
                    }
                    break;

                case "list":
                    listRecipes(player);
                    break;

                default:
                    // Try to parse as page number
                    try {
                        int page = Integer.parseInt(subCommand) - 1; // Convert to 0-based
                        if (page < 0) {
                            player.sendMessage(prefix + "§cPage number must be positive!");
                            return true;
                        }
                        openRecipeGUI(player, page);
                    } catch (NumberFormatException e) {
                        player.sendMessage(prefix + "§cUnknown subcommand: " + subCommand);
                        player.sendMessage(prefix + "§7Use §f/recipes help §7for available commands");
                    }
                    break;
            }
        } else {
            // Too many arguments
            player.sendMessage(prefix + "§cToo many arguments! Use §f/recipes help §cfor usage information");
        }

        return true;
    }

    /**
     * Opens the recipe GUI for a player at the specified page
     */
    private void openRecipeGUI(Player player, int page) {
        try {
            gui.openGUI(player, page);
        } catch (Exception e) {
            player.sendMessage(prefix + "§cFailed to open recipe viewer: " + e.getMessage());
            plugin.getLogger().severe("Error opening recipe GUI for " + player.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sends help message to the player
     */
    private void sendHelpMessage(Player player) {
        player.sendMessage("§8§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        player.sendMessage("§e§lRECIPES COMMAND HELP");
        player.sendMessage("");
        player.sendMessage("§6/recipes §7- Opens the recipe viewer GUI");
        player.sendMessage("§6/recipes <page> §7- Opens the recipe viewer at a specific page");
        player.sendMessage("§6/recipes help §7- Shows this help message");
        player.sendMessage("§6/recipes list §7- Lists all available recipe types");
        if (player.hasPermission("alphablockbreak.recipes.reload") || player.isOp()) {
            player.sendMessage("§6/recipes reload §7- Reloads all recipes (Admin only)");
        }
        if (player.hasPermission("alphablockbreak.recipes.test") || player.isOp()) {
            player.sendMessage("§6/recipes test §7- Runs comprehensive system tests (Admin only)");
        }
        player.sendMessage("");
        player.sendMessage("§7The recipe viewer shows all custom crafting recipes");
        player.sendMessage("§7available in the server. Use the navigation arrows");
        player.sendMessage("§7to browse through different recipes.");
        player.sendMessage("§8§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }

    /**
     * Reloads recipes and notifies the player
     */
    private void reloadRecipes(Player player) {
        try {
            gui.reloadRecipes();
            player.sendMessage(prefix + "§aSuccessfully reloaded " + gui.getTotalRecipes() + " recipes!");
        } catch (Exception e) {
            player.sendMessage(prefix + "§cFailed to reload recipes: " + e.getMessage());
            plugin.getLogger().severe("Error reloading recipes: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Lists all available recipe types
     */
    private void listRecipes(Player player) {
        int totalRecipes = gui.getTotalRecipes();
        if (totalRecipes == 0) {
            player.sendMessage(prefix + "§cNo recipes are currently available!");
            return;
        }

        player.sendMessage("§8§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        player.sendMessage("§e§lAVAILABLE CUSTOM RECIPES §7(" + totalRecipes + " total)");
        player.sendMessage("");
        player.sendMessage("§6§lBase Bomb Creation:");
        player.sendMessage("§7• Refillable Money Bomb T1 §8(4x Energium)");
        player.sendMessage("§7• Refillable Token Bomb T1 §8(8x Energium)");
        player.sendMessage("");
        player.sendMessage("§6§lCooldown Reduction:");
        player.sendMessage("§7• Money Bomb Cooldown -1min §8(4x Temporium)");
        player.sendMessage("§7• Token Bomb Cooldown -1min §8(4x Mystrium)");
        player.sendMessage("");
        player.sendMessage("§6§lTier Upgrades:");
        player.sendMessage("§7• T1 → T2 Upgrade §8(4x Voidstone)");
        player.sendMessage("§7• T2 → T3 Upgrade §8(4x Prismatic)");
        player.sendMessage("");
        player.sendMessage("§6§lSpecial Items:");
        player.sendMessage("§7• Pet Egg §8(8x Prismatic)");
        player.sendMessage("§7• Custom Beetroot Seed §8(4x Prismatic + Carrot Seed)");
        player.sendMessage("§7• Discount Skill Book §8(4x Prismatic + Book)");
        player.sendMessage("");
        player.sendMessage("§6§lOre Upgrades:");
        player.sendMessage("§7• Energium → Temporium §8(4x Energium)");
        player.sendMessage("§7• Temporium → Mystrium §8(4x Temporium)");
        player.sendMessage("§7• Mystrium → Voidstone §8(4x Mystrium)");
        player.sendMessage("§7• Voidstone → Prismatic §8(4x Voidstone)");
        player.sendMessage("");
        player.sendMessage("§6§lLegendary Weapons:");
        player.sendMessage("§7• Prismatic Blade §8(2x Prismatic + Stick)");
        player.sendMessage("");
        player.sendMessage("§7Use §f/recipes §7to open the recipe viewer!");
        player.sendMessage("§8§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }

    /**
     * Gets the GUI instance for external access
     */
    public RecipeViewerGUI getGUI() {
        return gui;
    }
}