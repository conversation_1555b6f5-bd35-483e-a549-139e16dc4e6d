package it.masterzen.Dungeon;

import de.tr7zw.nbtapi.NBT;
import de.tr7zw.nbtapi.iface.ReadableItemNBT;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;

/**
 * Manages Pet Egg functionality including block tracking and hatching
 */
public class PetEggManager implements Listener {

    private final String prefix = "§6§lPET EGG §8»§7 ";
    private final AlphaBlockBreak mainClass;

    public PetEggManager(AlphaBlockBreak mainClass) {
        this.mainClass = mainClass;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHand = player.getInventory().getItemInOffHand();
        
        // Check if player is holding a pet egg
        if (!isPetEgg(mainHand)) {
            return;
        }
        
        Integer blocksToHatch = NBT.get(mainHand, (Function<ReadableItemNBT, Integer>) nbt -> nbt.getInteger("blocksToHatch"));
        Integer blocksBroken = NBT.get(mainHand, (Function<ReadableItemNBT, Integer>) nbt -> nbt.getInteger("blocksBroken"));

        if (blocksToHatch == null || blocksBroken == null) {
            return;
        }

        // Increment blocks broken
        blocksBroken++;

        // Update NBT data
        Integer finalBlocksBroken = blocksBroken;
        NBT.modify(mainHand, nbt -> {
            nbt.setInteger("blocksBroken", finalBlocksBroken);
        });

        // Update lore to show progress
        updatePetEggLore(mainHand, blocksBroken, blocksToHatch);

        // Item is already modified in place, no need to set it again
        
        // Check if ready to hatch
        if (blocksBroken >= blocksToHatch) {
            hatchPetEgg(player);
        }
    }

    /**
     * Checks if an item is a pet egg
     */
    private boolean isPetEgg(ItemStack item) {
        if (item == null || item.getType() != Material.MONSTER_EGG) {
            return false;
        }
        
        if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        String displayName = item.getItemMeta().getDisplayName();
        if (!displayName.equals("§6§lPET EGG")) {
            return false;
        }
        
        // Check NBT data
        Boolean isPetEgg = NBT.get(item, (Function<ReadableItemNBT, Boolean>) nbt -> nbt.getBoolean("isPetEgg"));
        return isPetEgg != null && isPetEgg;
    }

    /**
     * Updates the pet egg lore with current progress
     */
    private void updatePetEggLore(ItemStack item, int blocksBroken, int blocksToHatch) {
        ItemMeta meta = item.getItemMeta();
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fA mysterious egg that will hatch");
        lore.add("§7| §finto a random pet after breaking");
        lore.add("§7| §e35,000 blocks §fwhile holding it!");
        lore.add("");
        lore.add("§e§lPROGRESS");
        lore.add("§e| §fBlocks Broken: §a" + blocksBroken + "§f/§e" + blocksToHatch);
        
        int remaining = Math.max(0, blocksToHatch - blocksBroken);
        if (remaining == 0) {
            lore.add("§e| §a§lREADY TO HATCH!");
        } else {
            lore.add("§e| §fRemaining: §c" + remaining + " blocks");
        }
        
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fHold this egg in offhand while mining");
        lore.add("§6| §fto progress towards hatching!");
        lore.add("");

        meta.setLore(lore);
        item.setItemMeta(meta);
    }

    /**
     * Hatches the pet egg and gives the player a random pet
     */
    private void hatchPetEgg(Player player) {
        // Remove the pet egg
        player.getInventory().setItemInMainHand(null);
        
        // Give random pet
        mainClass.giveRandomPet(player);
        
        // Send success message
        player.sendMessage(prefix + "§a§lCongratulations! Your pet egg has hatched!");
    }
}
