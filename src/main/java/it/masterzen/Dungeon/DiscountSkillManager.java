package it.masterzen.Dungeon;

import de.tr7zw.nbtapi.NBT;
import de.tr7zw.nbtapi.iface.ReadableItemNBT;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

import java.util.function.Function;

/**
 * Manages Discount Skill Book functionality including claiming and right-click handling
 */
public class DiscountSkillManager implements Listener {

    private final String prefix = "§6§lDISCOUNT SKILL §8»§7 ";
    private final AlphaBlockBreak mainClass;

    public DiscountSkillManager(AlphaBlockBreak mainClass) {
        this.mainClass = mainClass;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        // Only handle right-click actions
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        // Check if player is holding a discount skill book
        if (!isDiscountSkillBook(item)) {
            return;
        }

        // Cancel the event to prevent other interactions
        event.setCancelled(true);

        // Process the claim
        claimDiscountSkill(player, item);
    }

    /**
     * Checks if an item is a discount skill book
     */
    private boolean isDiscountSkillBook(ItemStack item) {
        if (item == null || item.getType() != Material.BOOK) {
            return false;
        }

        if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }

        String displayName = item.getItemMeta().getDisplayName();
        if (!displayName.equals("§6§lDISCOUNT SKILL §7Book")) {
            return false;
        }

        // Check NBT data
        Boolean isDiscountSkillBook = NBT.get(item, (Function<ReadableItemNBT, Boolean>) nbt -> nbt.getBoolean("isDiscountSkillBook"));
        return isDiscountSkillBook != null && isDiscountSkillBook;
    }

    /**
     * Processes the discount skill claim
     */
    private void claimDiscountSkill(Player player, ItemStack item) {
        PlayerData playerData = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        
        // Check current discount level
        int currentDiscount = playerData.getEnchantDiscount();
        
        // Check if already at maximum
        if (currentDiscount >= 10) {
            player.sendMessage(prefix + "§cYou already have the maximum enchant discount (10%)!");
            player.sendMessage(prefix + "§7You cannot use any more Discount Skill books.");
            return;
        }

        // Get discount amount from NBT (should be 1)
        Integer discountAmount = NBT.get(item, (Function<ReadableItemNBT, Integer>) nbt -> nbt.getInteger("discountAmount"));
        if (discountAmount == null) {
            discountAmount = 1; // Default value
        }

        // Add the discount (with cap at 10%)
        int oldDiscount = currentDiscount;
        playerData.addEnchantDiscount(discountAmount);
        int newDiscount = playerData.getEnchantDiscount();

        // Save to database
        try {
            mainClass.getMongoReader().savePlayerData(playerData, false);
        } catch (Exception e) {
            player.sendMessage(prefix + "§cError saving discount to database! Please try again.");
            e.printStackTrace();
            return;
        }

        // Remove the book from inventory
        if (item.getAmount() > 1) {
            item.setAmount(item.getAmount() - 1);
        } else {
            player.getInventory().setItemInMainHand(null);
        }

        // Send success messages
        player.sendMessage(prefix + "§a§lDiscount Skill Claimed!");
        player.sendMessage(prefix + "§fEnchant Discount: §e" + oldDiscount + "% §f→ §a" + newDiscount + "%");
        
        if (newDiscount < 10) {
            int remaining = 10 - newDiscount;
            player.sendMessage(prefix + "§7You can still claim §e" + remaining + "% §7more discount!");
        } else {
            player.sendMessage(prefix + "§6§lMaximum discount reached! §7All enchantments are now 10% cheaper!");
        }
    }
}
