package it.masterzen.Dungeon;

import de.tr7zw.nbtapi.NBT;
import de.tr7zw.nbtapi.iface.ReadableItemNBT;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;

/**
 * Manager class for creating and handling custom ores from the Dungeon Finder enchant.
 * Handles NBT tagging, lore application, and drop rate calculations.
 */
public class CustomOreManager {
    
    // NBT key constants for custom ore identification
    public static final String CUSTOM_ORE_KEY = "custom_ore_type";
    public static final String CUSTOM_ORE_SOURCE = "dungeon_finder";
    
    /**
     * Creates a custom ore ItemStack with proper NBT tags and mystical lore
     * @param oreType The type of custom ore to create
     * @param quantity The amount of ore to create (stack size)
     * @return ItemStack with NBT tags and lore applied
     */
    public static ItemStack createCustomOre(CustomOre oreType, int quantity) {
        ItemStack ore = new ItemStack(oreType.getMaterial(), Math.max(1, quantity));
        ItemMeta meta = ore.getItemMeta();
        
        // Set display name
        meta.setDisplayName(oreType.getDisplayName());
        
        // Create lore with mystical descriptions
        List<String> lore = new ArrayList<>(oreType.getLore());
        lore.add(""); // Empty line separator
        lore.add("§8§l» §7Discovered through §e§lDungeon Finder");
        lore.add("§8§l» §7A mystical ore for future crafting");
        
        meta.setLore(lore);
        
        // Hide item attributes for cleaner appearance
        meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);
        meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        
        ore.setItemMeta(meta);
        
        // Apply NBT tags for crafting system identification
        NBT.modify(ore, nbt -> {
            nbt.setString(CUSTOM_ORE_KEY, oreType.getNbtTag());
            nbt.setString(CUSTOM_ORE_SOURCE, "dungeon_finder");
            // nbt.setLong("discovery_time", System.currentTimeMillis());
        });
        
        return ore;
    }
    
    /**
     * Checks if an ItemStack is a custom ore from Dungeon Finder
     * @param item The ItemStack to check
     * @return true if it's a custom ore, false otherwise
     */
    public static boolean isCustomOre(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        return NBT.get(item, nbt -> {
            return nbt.hasTag(CUSTOM_ORE_KEY) && nbt.hasTag(CUSTOM_ORE_SOURCE);
        });
    }
    
    /**
     * Gets the custom ore type from an ItemStack
     * @param item The ItemStack to check
     * @return The CustomOre type, or null if not a custom ore
     */
    public static CustomOre getCustomOreType(ItemStack item) {
        if (!isCustomOre(item)) {
            return null;
        }
        
        String oreTag = NBT.get(item, (Function<ReadableItemNBT, String>) nbt -> nbt.getString(CUSTOM_ORE_KEY));
        
        for (CustomOre ore : CustomOre.values()) {
            if (ore.getNbtTag().equals(oreTag)) {
                return ore;
            }
        }
        
        return null;
    }
    
    /**
     * Attempts to generate a custom ore based on enchant level and drop rates
     * @param enchantLevel The level of the Dungeon Finder enchant
     * @return The generated CustomOre, or null if no ore was generated
     */
    public static CustomOre generateRandomOre(int enchantLevel) {
        if (enchantLevel <= 0) {
            return null;
        }
        
        // Generate random number for drop chance (0-100)
        double random = ThreadLocalRandom.current().nextDouble(100.0);
        double cumulativeChance = 0.0;
        
        // Check each ore type in order of rarity (most common first)
        for (CustomOre ore : CustomOre.values()) {
            double dropRate = ore.getDropRateForLevel(enchantLevel);
            cumulativeChance += dropRate;
            
            if (random < cumulativeChance) {
                return ore;
            }
        }
        
        // No ore generated (most common outcome)
        return null;
    }
    
    /**
     * Gets the total drop chance for any ore at a specific enchant level
     * @param enchantLevel The level of the Dungeon Finder enchant
     * @return The total percentage chance of getting any ore
     */
    public static double getTotalDropChance(int enchantLevel) {
        double total = 0.0;
        for (CustomOre ore : CustomOre.values()) {
            total += ore.getDropRateForLevel(enchantLevel);
        }
        return total;
    }
    
    /**
     * Gets formatted drop rate information for display in lore
     * @param enchantLevel The level of the Dungeon Finder enchant
     * @return List of formatted strings showing drop rates
     */
    public static List<String> getDropRateInfo(int enchantLevel) {
        List<String> info = new ArrayList<>();
        info.add("§e§lDROP RATES §7(Level " + enchantLevel + "):");
        
        for (CustomOre ore : CustomOre.values()) {
            double rate = ore.getDropRateForLevel(enchantLevel);
            String rateStr = String.format("%.2f", rate);
            info.add("§7• " + ore.getDisplayName() + "§7: §a" + rateStr + "%");
        }
        
        double totalRate = getTotalDropChance(enchantLevel);
        String totalStr = String.format("%.2f", totalRate);
        info.add("§7• §e§lTotal Chance: §a" + totalStr + "%");
        
        return info;
    }
}
