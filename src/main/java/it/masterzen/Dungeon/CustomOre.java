package it.masterzen.Dungeon;

import org.bukkit.Material;

import java.util.Arrays;
import java.util.List;

/**
 * Enum representing the custom ores that can be found with the Dungeon Finder enchant.
 * Each ore has mystical properties and will be used for future custom crafting systems.
 */
public enum CustomOre {
    
    ENERGIUM(
        "§b§lENERGIUM §7Crystal",
        Material.DIAMOND,
        "energium_crystal",
        5.0, // Base drop rate at level 10
        Arrays.asList(
            "§7A pulsating crystal that radiates with",
            "§7ancient energy from the depths of the",
            "§7dungeon. Its azure glow seems to",
            "§7whisper secrets of forgotten power.",
            "",
            "§e§lMYSTICAL PROPERTIES:",
            "§e• Contains raw elemental energy",
            "§e• Resonates with magical frequencies",
            "§e• Essential for advanced enchantments",
            "",
            "§6§lRARELY FOUND §7in the deepest dungeons..."
        )
    ),
    
    TEMPORIUM(
        "§d§lTEMPORIUM §7Shard",
        Material.EMERALD,
        "temporium_shard",
        2.0, // Base drop rate at level 10
        Arrays.asList(
            "§7A crystalline fragment that seems to",
            "§7bend time itself around its edges.",
            "§7Gazing into its depths reveals glimpses",
            "§7of moments yet to come and past.",
            "",
            "§e§lMYSTICAL PROPERTIES:",
            "§e• Manipulates temporal flow",
            "§e• Stores echoes of ancient magic",
            "§e• Key component for time-based crafts",
            "",
            "§6§lEXTREMELY RARE §7temporal anomaly..."
        )
    ),
    
    MYSTRIUM(
        "§5§lMYSTRIUM §7Essence",
        Material.END_CRYSTAL,
        "mystrium_essence",
        1.0, // Base drop rate at level 10
        Arrays.asList(
            "§7A swirling orb of pure mystical energy",
            "§7that defies comprehension. It pulses",
            "§7with otherworldly power and seems to",
            "§7exist between dimensions.",
            "",
            "§e§lMYSTICAL PROPERTIES:",
            "§e• Pure concentrated magic",
            "§e• Bridges dimensional barriers",
            "§e• Core ingredient for legendary items",
            "",
            "§6§lLEGENDARY §7essence of pure mystery..."
        )
    ),
    
    VOIDSTONE(
        "§8§lVOIDSTONE §7Fragment",
        Material.OBSIDIAN,
        "voidstone_fragment",
        0.5, // Base drop rate at level 10
        Arrays.asList(
            "§7A fragment of absolute darkness that",
            "§7seems to absorb light itself. This piece",
            "§7of the void carries the weight of",
            "§7infinite emptiness and untold power.",
            "",
            "§e§lMYSTICAL PROPERTIES:",
            "§e• Harbors void energy",
            "§e• Nullifies opposing forces",
            "§e• Foundation for ultimate weapons",
            "",
            "§6§lMYTHICAL §7remnant of the void..."
        )
    ),
    
    PRISMATIC(
        "§f§lPRISMATIC §7Core",
        Material.NETHER_STAR,
        "prismatic_core",
        0.1, // Base drop rate at level 10
        Arrays.asList(
            "§7The ultimate crystalline formation that",
            "§7contains the essence of all elements.",
            "§7Its surface shifts through every color",
            "§7imaginable, holding infinite potential.",
            "",
            "§e§lMYSTICAL PROPERTIES:",
            "§e• Contains all elemental essences",
            "§e• Amplifies any magical effect",
            "§e• The pinnacle of crafting materials",
            "",
            "§6§lASCENDED §7perfection of creation..."
        )
    );
    
    private final String displayName;
    private final Material material;
    private final String nbtTag;
    private final double baseDropRate;
    private final List<String> lore;
    
    CustomOre(String displayName, Material material, String nbtTag, double baseDropRate, List<String> lore) {
        this.displayName = displayName;
        this.material = material;
        this.nbtTag = nbtTag;
        this.baseDropRate = baseDropRate;
        this.lore = lore;
    }
    
    /**
     * Gets the display name of the ore
     * @return The formatted display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the Bukkit material for this ore
     * @return The Material type
     */
    public Material getMaterial() {
        return material;
    }
    
    /**
     * Gets the NBT tag identifier for this ore
     * @return The NBT tag string
     */
    public String getNbtTag() {
        return nbtTag;
    }
    
    /**
     * Gets the base drop rate at level 10 (in percentage)
     * @return The base drop rate
     */
    public double getBaseDropRate() {
        return baseDropRate;
    }
    
    /**
     * Gets the mystical lore for this ore
     * @return List of lore strings
     */
    public List<String> getLore() {
        return lore;
    }
    
    /**
     * Calculates the drop rate for a specific enchant level
     * @param enchantLevel The level of the Dungeon Finder enchant (1-10)
     * @return The drop rate percentage for this level
     */
    public double getDropRateForLevel(int enchantLevel) {
        if (enchantLevel <= 0) return 0.0;
        if (enchantLevel > 10) enchantLevel = 10;

        // Linear scaling: level 1 = 1% of base rate, level 10 = 100% of base rate
        // This creates the 1000% difference as requested (level 10 is 10x level 1)
        // Level 1: 0.1 * baseDropRate, Level 10: 1.0 * baseDropRate
        double scaleFactor = (enchantLevel - 1) * 0.1 + 0.1;
        return baseDropRate * scaleFactor;
    }
}
