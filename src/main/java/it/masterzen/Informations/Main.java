package it.masterzen.Informations;

import it.masterzen.MobCoin.GUI;
import it.masterzen.blockbreak.AlphaBlockBreak;
import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.TextComponent;
import org.apache.commons.lang3.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class Main implements CommandExecutor, Listener {

    private final String prefix = "§e§lALPHA §8»§7 ";
    private List<String> features = new ArrayList<>();

    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        insertFeatures();
    }

    public void insertFeatures() {
        features.add("Mine");
        features.add("Tokens");
        //features.add("Ranks");
        features.add("Keys");
        features.add("Payouts");
        features.add("Islands");
        features.add("Spawners");
        features.add("XP");
        features.add("Prestige");
        features.add("Rebirth");
        features.add("Farmer");
        features.add("SkillTree");
        features.add("Competition");
        features.add("ServerGoal");
        features.add("Withdraw");
    }

    public void sendFeatureList(Player player) {
        TextComponent message = new TextComponent("");

        player.sendMessage("§e§lFEATURES §f §7List");
        player.sendMessage("§7§o(Click me)");
        message.setText("    §7- §fMine");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fTokens");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        //message.setText("    §7- §fRanks");
        //message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        //player.spigot().sendMessage(message);

        message.setText("    §7- §fKeys");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fPayouts");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fIslands");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fSpawners");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fXP");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fPrestige");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fRebirth");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fFarmer");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fSkillTree");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fCompetition");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fServerGoal");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        message.setText("    §7- §fWithdraw");
        message.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/feature " + message.getText().replace("    §7- §f", "")));
        player.spigot().sendMessage(message);

        player.sendMessage("");

    }

    public void sendWiki(Player player, String feature) {
        player.sendMessage("");
        player.sendMessage(StringUtils.center("§6§l" + feature.toUpperCase(), 81));

        if (feature.equalsIgnoreCase("Mine")) {
            player.sendMessage("    §fType §e/mine §fto create your own individual mine");
            player.sendMessage("    §fUse the §e/mine §fGUI to access mine types, upgrades and more");
            player.sendMessage("    §fYou can add players to your mine via §e/mine add [username]");
        } else if (feature.equalsIgnoreCase("Tokens")) {
            player.sendMessage("    §fTokens are used to upgrade your pickaxe");
            player.sendMessage("    §fRight click your pickaxe to open the Enchant Menu");
            player.sendMessage("    §fTokens are gained by §eMining, Spawners, XP §fand much more");
            player.sendMessage("    §fYou can send tokens to other players via §e/te pay");
        } else if (feature.equalsIgnoreCase("Ranks")) {

        } else if (feature.equalsIgnoreCase("Keys")) {
            player.sendMessage("    §fUnlock Keys to receive OP Rewards !");
            player.sendMessage("    §fUpgrade KeyFinder to receive unique keys while mining.");
            player.sendMessage("    §fEvery §e250 §flevels, you will unlock a new type of key");
            player.sendMessage("    §fKeys can also be found in the §eMobcoin shop,");
            player.sendMessage("    §ePrestigeShop§f, and more");
            player.sendMessage("    §fAccess your keys via the custom-coded §e/key §fsystem");
        } else if (feature.equalsIgnoreCase("Payouts")) {
            player.sendMessage("    §fPayouts on Alpha Prison are rewarded to the members on");
            player.sendMessage("    §fan §eisland §f with the §ehighest island level §f(§e/is top level§f).");
            player.sendMessage("    §f§l1st Place: §e$50 PayPal §fOR §e$100 Buycraft");
            player.sendMessage("    §f§l2nd Place: §e$50 Buycraft");
            player.sendMessage("    §f§l3rd Place: §e$20 Buycraft");
        } else if (feature.equalsIgnoreCase("Islands")) {
            player.sendMessage("    §fCreate an island via §e/is create");
            player.sendMessage("    §fYou can have up to §e6 §fplayers on your island");
            player.sendMessage("    §fTo obtain a place on §e/is top level§f, you need");
            player.sendMessage("    §fto §eplace beacons §fon your island (§e/is§f).");
            player.sendMessage("    §fEvery §e100 beacons §fis equal to §e1 island level§f.");
        } else if (feature.equalsIgnoreCase("Spawners")) {
            player.sendMessage("    §fPlace spawners on your island to receive the rewards");
            player.sendMessage("    §fYou must be near the spawner for it to work");
            player.sendMessage("    §fWe have §e4 §ftypes of Spawners:");
            player.sendMessage("    §eMoney, Tokens, Silverfish §fand §eMobcoins");
            player.sendMessage("    §fCollect the items using Hoppers and Chests");
            player.sendMessage("    §fAccess the MobCoin shop via §e/mobcoin");
        } else if (feature.equalsIgnoreCase("XP")) {
            player.sendMessage("    §fXP is gained by Mining and through Crystals");
            player.sendMessage("    §fConvert your XP into §eMoney §for §eTokens §fvia §e/xpshop");
            player.sendMessage("    §fIncrease the XP you gain from mining via");
            player.sendMessage("    §fthe XPShop Upgrade");
            player.sendMessage("    §fType §e/c shop §fto convert your Crystals into XP");
        } else if (feature.equalsIgnoreCase("Prestige")) {
            player.sendMessage("    §fPrestiging is the main way of levelling up,");
            player.sendMessage("    §ftype §e/prestige §fin game to gain higher levels.");
            player.sendMessage("    §fWith our §ecustom-coded §fmine system,");
            player.sendMessage("    §fas you prestige, the more");
            player.sendMessage("    §fvaluable your mine becomes.");
        } else if (feature.equalsIgnoreCase("Rebirth")) {
            player.sendMessage("    §fThe command §e/rebirth §fis unlocked after");
            player.sendMessage("    §freaching §e25k prestiges§f. For the first");
            player.sendMessage("    §f§efive rebirths§f, you will receive a §erandom");
            player.sendMessage("    §fperk from §e/perks§f. After that, you will begin");
            player.sendMessage("    §freceiving 1-2 §erebirth points §fwhich can be");
            player.sendMessage("    §fused in the §e/rebirth shop§f. This feature lets you");
            player.sendMessage("    §fincrease enchants beyond their limit.");
        } else if (feature.equalsIgnoreCase("Farmer")) {
            player.sendMessage("    §fType §e/warp farmer §fto start farming");
            player.sendMessage("    §fBreak the fully grown wheat to receive Farmer Points");
            player.sendMessage("    §fUse the points in the §e/farmer shop §fto purchase");
            player.sendMessage("    §funlimited levels of §eNuke §fand §eJackhammer");
        } else if (feature.equalsIgnoreCase("SkillTree")) {
            player.sendMessage("    §fGain skillpoints to use in §e/skills");
            player.sendMessage("    §fto unlock unique boosters");
            player.sendMessage("    §fThe skilltree contains skills for");
            player.sendMessage("    §eXP, Tokens, Money, Keys, Dungeon §fand §eJackhammer");
            player.sendMessage("    §fYou will get §e1 skillpoint§f for every");
            player.sendMessage("    §e50k §fblocks you break");
        } else if (feature.equalsIgnoreCase("Competition")) {
            player.sendMessage("    §fType §e/competition §fto see the daily challenge");
            player.sendMessage("    §fThere are 3 types of Challenges:");
            player.sendMessage("    §eLumberjack, Farmer §fand §eMiner");
            player.sendMessage("    §fView the leaderboard with §e/comp top");
            player.sendMessage("    §fWin the daily competition to receive Buycraft credits");
        } else if (feature.equalsIgnoreCase("ServerGoal")) {
            player.sendMessage("    §fWork together to mine blocks and achieve the server goal");
            player.sendMessage("    §fType §e/sg lb §fto check the Server Goal Leaderboard");
            player.sendMessage("    §fIf you achieve the goal, by mining §e7.5k §fblocks");
            player.sendMessage("    §fyou will receive a percentage of the combined");
            player.sendMessage("    §ftoken balance of the server");
            player.sendMessage("    §fThe 3 players who mine the most blocks");
            player.sendMessage("    §fwill receive more tokens");
        } else if (feature.equalsIgnoreCase("Withdraw")) {
            player.sendMessage("    §fWe have a custom Withdraw system");
            player.sendMessage("    §fYou can withdraw Money, Tokens and XP");
            player.sendMessage("    §fby typing /withdraw [currency] [amount] (times)");
        } else {
            player.sendMessage(prefix + "§cThere's no wiki for this feature. §7You can always suggest to add it through §a/Discord");
        }

        player.sendMessage("");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("feature")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 1) {
                    sendWiki(player, args[0]);
                    /*if (features.contains(args[0])) {
                        sendWiki(player, args[0]);
                    } else {
                        player.sendMessage(prefix + "§cThere's no wiki for this feature. §7You can always suggest to add it through §a/Discord");
                    }*/
                } else {
                    sendFeatureList(player);
                }
            }
        }
        return false;
    }
}
