package it.masterzen.AuctionHouse;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.milkbowl.vault.economy.Economy;
import org.apache.commons.io.FilenameUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import javax.security.auth.callback.TextInputCallback;
import java.io.File;
import java.io.IOException;
import java.util.*;

public class AuctionHouse implements CommandExecutor, Listener {

    private final AlphaBlockBreak mainClass;
    Economy economy;

    private final String prefix = "§e§lAH §8»§7 ";

    private static YamlConfiguration ymlFile;
    private final List<UUID> seller = new ArrayList<>();
    private final List<Integer> cornerIndexes = new ArrayList<>();
    private HashMap<Player, UUID> openedShops = new HashMap<>();

    private void setupCorners() {
        cornerIndexes.add(0);
        cornerIndexes.add(1);
        cornerIndexes.add(2);
        cornerIndexes.add(3);
        cornerIndexes.add(4);
        cornerIndexes.add(5);
        cornerIndexes.add(6);
        cornerIndexes.add(7);
        cornerIndexes.add(8);
        cornerIndexes.add(9);

        cornerIndexes.add(18);
        cornerIndexes.add(27);
        cornerIndexes.add(36);
        cornerIndexes.add(45);

        cornerIndexes.add(17);
        cornerIndexes.add(26);
        cornerIndexes.add(35);
        cornerIndexes.add(44);

        cornerIndexes.add(46);
        cornerIndexes.add(47);
        cornerIndexes.add(48);
        cornerIndexes.add(49);
        cornerIndexes.add(50);
        cornerIndexes.add(51);
        cornerIndexes.add(52);
        cornerIndexes.add(53);
    }

    public AuctionHouse(AlphaBlockBreak plugin) {
        mainClass = plugin;
        economy = mainClass.getEconomy();
        setupCorners();
    }

    public boolean isAtCorner(int index) {
        return cornerIndexes.contains(index);
    }

    public void openAuctionHouse(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lAUCTION §f| §7House");
        mainClass.FillBorder(gui);
        int index = 10;
        boolean isAddedToGui = false;
        int safezone;

        File mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Shops/");
        if (mainFolder.exists()) {
            File[] sellerList = mainFolder.listFiles();
            assert sellerList != null;
            for (File tmpSeller : sellerList) {
                UUID tmpUUID = UUID.fromString(FilenameUtils.removeExtension(tmpSeller.getName()));
                if (!seller.contains(tmpUUID)) {
                    seller.add(tmpUUID);
                }
            }
        }

        if (!seller.isEmpty()) {
            for (UUID tmpSeller : seller.subList(0, seller.size())) {
                if (tmpSeller != player.getUniqueId()) {
                    safezone = 100;
                    isAddedToGui = false;

                    ItemStack head = new ItemStack(Material.SKULL_ITEM, 1, (byte) 3);
                    SkullMeta headMeta = (SkullMeta) head.getItemMeta();
                    OfflinePlayer tmpPlayer = Bukkit.getOfflinePlayer(tmpSeller);
                    headMeta.setOwningPlayer(tmpPlayer);
                    headMeta.setDisplayName("§7" + tmpPlayer.getName() + "'s Shop");
                    head.setItemMeta(headMeta);

                    while (!isAddedToGui && safezone > 0) {
                        if (!isAtCorner(index) && index < gui.getSize()) {
                            gui.setItem(index, head);
                            isAddedToGui = true;
                        }
                        index++;
                        safezone--;
                    }
                }
            }
        } /*else {
            ItemStack noShops = new ItemStack(Objects.requireNonNull(XMaterial.RED_STAINED_GLASS.parseItem()));
            ItemMeta itemMeta = noShops.getItemMeta();
            itemMeta.setDisplayName("§cNo Shops Available");
            noShops.setItemMeta(itemMeta);
            gui.setItem(22, noShops);
        }*/

        ItemStack vault = new ItemStack(Material.CHEST);
        ItemMeta meta = vault.getItemMeta();
        meta.setDisplayName("§6§lVAULT");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Click me to open your Vault");
        lore.add("");
        meta.setLore(lore);
        vault.setItemMeta(meta);

        gui.setItem(49, vault);
        player.openInventory(gui);
    }

    public void addItem(Player player, ItemStack item, long price) throws IOException {
        UUID playerUUID = player.getUniqueId();
        if (!seller.contains(playerUUID)) {
            seller.add(playerUUID);
        }

        File mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }
        mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Shops/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }

        File file = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Shops/" + playerUUID.toString() + ".yml");
        if (!file.exists()) {
            file.createNewFile();
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        int lastIndex = 0;
        boolean found = false;
        while (!found) {
            if (ymlFile.getItemStack(lastIndex + ".item") != null && ymlFile.getItemStack(lastIndex + ".item").equals(item)) {
                player.sendMessage(prefix + "§cYou are already buying this item");
                return;
            }
            if (!ymlFile.contains(lastIndex + ".item")) {
                found = true;
            } else {
                lastIndex++;
            }
            if (lastIndex > 53) {
                found = true;
            }
        }

        if (lastIndex > 53) {
            player.sendMessage(prefix + "§cYou reached the max amount of item in your store !");
        } else {
            item.setAmount(1);
            ymlFile.set(lastIndex + ".item", item);
            ymlFile.set(lastIndex + ".price", price);
            ymlFile.save(file);

            player.sendMessage(prefix + "§aItem added to your shop !");
        }
    }

    public void addItemToVault(UUID shopOwner, Player whoSold, ItemStack item, long amount) throws IOException {
        if (!seller.contains(shopOwner)) {
            seller.add(shopOwner);
        }

        File mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }
        mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }

        File file = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/" + shopOwner.toString() + ".yml");
        if (!file.exists()) {
            file.createNewFile();
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        int lastIndex = 0;
        int firstFreeSlot = 0;
        int foundedItemSlot = 0;
        boolean found = false;
        boolean firstFreeSlotFounded = false;
        while (lastIndex < 53) {
            if (ymlFile.getItemStack(lastIndex + ".item") != null && ymlFile.getItemStack(lastIndex + ".item").isSimilar(item)) {
                found = true;
                foundedItemSlot = lastIndex;
            }
            if (ymlFile.getItemStack(lastIndex + ".item") == null && !firstFreeSlotFounded) {
                firstFreeSlot = lastIndex;
                firstFreeSlotFounded = true;
            }
            lastIndex++;
        }

        if (found) {
            long currentAmount = ymlFile.getLong(foundedItemSlot + ".amount");
            ymlFile.set(foundedItemSlot + ".amount", currentAmount + amount);
        } else {
            lastIndex = firstFreeSlot;
            ymlFile.set(lastIndex + ".item", item);
            ymlFile.set(lastIndex + ".amount", amount);
        }

        if (Bukkit.getPlayer(shopOwner) != null) {
            if (item.hasItemMeta() && item.getItemMeta().getDisplayName() != null) {
                Bukkit.getPlayer(shopOwner).sendMessage(prefix + whoSold.getName() + " sold you §a" + amount + "x " + item.getItemMeta().getDisplayName());
            } else {
                Bukkit.getPlayer(shopOwner).sendMessage(prefix + whoSold.getName() + " sold you §a" + amount + "x " + item.getType().toString().replace("_", " "));
            }

        }
        ymlFile.save(file);
    }

    public void withdrawItemToVault(UUID shopOwner, int index, int amount, boolean withdrawAll) throws IOException {
        Player player = Bukkit.getPlayer(shopOwner);
        if (!seller.contains(shopOwner)) {
            seller.add(shopOwner);
        }

        File mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }
        mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }

        File file = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/" + shopOwner.toString() + ".yml");
        if (!file.exists()) {
            file.createNewFile();
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        long amountWithdrawable = ymlFile.getLong(index + ".amount");
        int playerTotalFreeSlot = mainClass.getEmptySlots(player.getInventory());

        if (withdrawAll) {
            if (amount > amountWithdrawable) {
                amount = (int) amountWithdrawable;
            } else {
                amount = playerTotalFreeSlot * 64;
            }
            /*if (amount > playerTotalFreeSlot * 64) {
                amount = playerTotalFreeSlot * 64;
            } else {
                if (amount > amountWithdrawable) {
                    amount = (int) amountWithdrawable;
                } else {
                    amount = playerTotalFreeSlot * 64;
                }
            }*/
        } else {
            /*if (playerTotalFreeSlot * 64 < amount) {
                if (playerTotalFreeSlot * 64L >= amountWithdrawable) {
                    amount = playerTotalFreeSlot * 64;
                } else {
                    amount = (int) amountWithdrawable;
                }
            }*/
            if (amount <= amountWithdrawable) {
                if (amount > playerTotalFreeSlot * 64) {
                    amount = playerTotalFreeSlot * 64;
                }
            } else {
                amount = (int) amountWithdrawable;
            }
        }

        if (amount > 0) {
            if (amountWithdrawable > 0) {
                ItemStack itemToAdd = ymlFile.getItemStack(index + ".item").clone();
                itemToAdd.setAmount(amount);
                player.getInventory().addItem(itemToAdd);
                if (ymlFile.getItemStack(index + ".item").hasItemMeta() && ymlFile.getItemStack(index + ".item").getItemMeta().getDisplayName() != null) {
                    player.sendMessage(prefix + amount + "x " + ymlFile.getItemStack(index + ".item").getItemMeta().getDisplayName() + "§7 added to your inventory from Vault");
                } else {
                    player.sendMessage(prefix + amount + "x " + ymlFile.getItemStack(index + ".item").getType().toString().replace("_", "") + "§7 added to your inventory from Vault");
                }

                if (amountWithdrawable - amount < 0) {
                    ymlFile.set(index + ".item", "");
                    ymlFile.set(index + ".amount", "");
                } else {
                    ymlFile.set(index + ".amount", amountWithdrawable - amount);
                }
            } else {
                ymlFile.set(index + ".amount", 0);
            }
        } else {
            player.sendMessage(prefix + "§cInventory Full !");
        }
        ymlFile.save(file);
    }

    public void openPlayerShop(Player player, OfflinePlayer shopOwner) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lAUCTION §f| §7House");
        File file = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Shops/" + shopOwner.getUniqueId().toString() + ".yml");

        ymlFile = YamlConfiguration.loadConfiguration(file);
        boolean itemFound = true;
        int index = 0;
        int safezone = 64;

        while (itemFound && safezone > 0) {
            if (ymlFile.contains(index + ".item")) {
                ItemStack item = ymlFile.getItemStack(index + ".item");
                ItemMeta itemMeta = item.getItemMeta();
                List<String> lore = new ArrayList<>();
                if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
                    lore.addAll(item.getItemMeta().getLore());
                }
                lore.add("");
                lore.add("§7Sell this item for: §a§l" + mainClass.newFormatNumber(ymlFile.getLong(index + ".price"), player));
                lore.add("");
                lore.add("§7Left Click: Sell 1x");
                lore.add("§7Right Click: Sell 10x");
                lore.add("§7Q: Sell All");
                itemMeta.setLore(lore);
                item.setItemMeta(itemMeta);
                gui.setItem(index, item);
            } else {
                itemFound = false;
            }
            index++;
            safezone--;
        }

        openedShops.remove(player);
        openedShops.put(player, shopOwner.getUniqueId());
        player.openInventory(gui);
    }

    public void openVault(Player player) throws IOException {
        File mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }
        mainFolder = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/");
        if (!mainFolder.exists()) {
            mainFolder.mkdirs();
        }

        File file = new File(System.getProperty("user.dir") + "/plugins/AuctionHouse/Vault/" + player.getUniqueId().toString() + ".yml");
        if (!file.exists()) {
            file.createNewFile();
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lAUCTION §f| §7Vault");
        int currentIndex = 0;
        while (currentIndex < 53) {
            if (ymlFile.contains(currentIndex + ".item") && ymlFile.getLong(currentIndex + ".amount") > 0) {
                ItemStack tmpitem = ymlFile.getItemStack(currentIndex + ".item");
                ItemMeta meta = tmpitem.getItemMeta();
                List<String> lore = new ArrayList<>();
                if (tmpitem.hasItemMeta() && tmpitem.getItemMeta().hasLore()) {
                    lore.addAll(tmpitem.getItemMeta().getLore());
                }
                lore.add("");
                lore.add("§7Total Amount to Withdraw: §a§l" + ymlFile.getLong(currentIndex + ".amount"));
                lore.add("");
                lore.add("§7Left Click: Withdraw 1x");
                lore.add("§7Right Click: Withdraw 10x");
                lore.add("§7Q: Withdraw All");
                meta.setLore(lore);
                tmpitem.setItemMeta(meta);
                gui.setItem(currentIndex, tmpitem);
            }
            currentIndex++;
        }
        player.openInventory(gui);
    }

    public List<String> removeLore(List<String> lore) {
        for (int i = 0; i < 6; i++) {
            lore.remove(lore.size() - 1);
        }

        return lore;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getCurrentItem() != null) {
                if (event.getView().getTitle().equals("§e§lAUCTION §f| §7House")) {
                    event.setCancelled(true);
                    ItemStack clickedItem = event.getCurrentItem();
                    Player player = (Player) event.getWhoClicked();
                    if (clickedItem != null && !clickedItem.getType().equals(Material.AIR)) {
                        if (event.getSlot() == 49) {
                            openVault(player);
                        } else if (clickedItem.getType().equals(Material.SKULL_ITEM)) {
                            String shopOwner = clickedItem.getItemMeta().getDisplayName();
                            shopOwner = ChatColor.stripColor(shopOwner);
                            shopOwner = shopOwner.replace("'s Shop", "");
                            player.closeInventory();
                            openPlayerShop(player, Bukkit.getOfflinePlayer(shopOwner));
                        } else if (!clickedItem.getType().equals(XMaterial.RED_STAINED_GLASS_PANE.parseMaterial())) {
                            ItemStack item = clickedItem.clone();
                            ItemMeta tmpMeta = clickedItem.getItemMeta();
                            tmpMeta.setLore(removeLore(tmpMeta.getLore()));
                            item.setItemMeta(tmpMeta);
                            ItemStack inventoryItem = null;
                            for (ItemStack tmpitem : player.getInventory().getContents()) {
                                if (tmpitem != null && tmpitem.isSimilar(item)) {
                                    inventoryItem = tmpitem;
                                }
                            }
                            int amountToAddToSeller = 0;
                            if (inventoryItem != null) {
                                int playerItemAmount = inventoryItem.getAmount();
                                if (event.getClick().isLeftClick()) {
                                    if (playerItemAmount > 1) {
                                        inventoryItem.setAmount(inventoryItem.getAmount() - 1);
                                    } else if (playerItemAmount == 1) {
                                        boolean removed = false;
                                        for (int i = 0; i < player.getInventory().getStorageContents().length; i++) {
                                            if (!removed && player.getInventory().getItem(i) != null && player.getInventory().getItem(i).isSimilar(inventoryItem)) {
                                                player.getInventory().clear(i);
                                                removed = true;
                                            }
                                        }
                                    }
                                    amountToAddToSeller = 1;
                                } else if (event.getClick().isRightClick()) {
                                    if (playerItemAmount > 10) {
                                        inventoryItem.setAmount(inventoryItem.getAmount() - 10);
                                    } else if (playerItemAmount == 10) {
                                        boolean removed = false;
                                        for (int i = 0; i < player.getInventory().getStorageContents().length; i++) {
                                            if (!removed && player.getInventory().getItem(i) == inventoryItem) {
                                                player.getInventory().remove(i);
                                                removed = true;
                                            }
                                        }
                                    } else {
                                        player.sendMessage(prefix + "§cYou don't have anough item to sell");
                                        return;
                                    }
                                    amountToAddToSeller = 10;
                                } else if (event.getAction().equals(InventoryAction.DROP_ONE_SLOT)) {
                                    int totalAmount = 0;
                                    for (ItemStack itemSlot : player.getInventory().getStorageContents()) {
                                        if (itemSlot != null && itemSlot.isSimilar(inventoryItem)) {
                                            totalAmount = totalAmount + itemSlot.getAmount();
                                        }
                                    }
                                    player.getInventory().remove(inventoryItem);
                                    amountToAddToSeller = totalAmount;
                                }
                                long price = 0;
                                if (amountToAddToSeller > 0) {
                                    List<String> tmpLore = clickedItem.clone().getItemMeta().getLore();
                                    for (String line : tmpLore) {
                                        if (line.contains("§7Sell this item for: ")) {
                                            String tmp = line;
                                            tmp = ChatColor.stripColor(tmp);
                                            tmp = tmp.replace("Sell this item for: ", "");
                                            if (tmp.contains("Qt")) {
                                                tmp = tmp.replace("Qt", "000000000000000000");
                                            } else if (tmp.contains("Q")) {
                                                tmp = tmp.replace("Q", "000000000000000");
                                            } else if (tmp.contains("T")) {
                                                tmp = tmp.replace("T", "000000000000");
                                            } else if (tmp.contains("B")) {
                                                tmp = tmp.replace("B", "000000000");
                                            } else if (tmp.contains("M")) {
                                                tmp = tmp.replace("M", "000000");
                                            } else if (tmp.contains("k")) {
                                                tmp = tmp.replace("k", "000");
                                            }
                                            price = Long.parseLong(tmp) * amountToAddToSeller;
                                        }
                                    }
                                    if (price > 0) {
                                        OfflinePlayer shopOwner = Bukkit.getOfflinePlayer(openedShops.get(player));
                                        if (economy == null) {
                                            economy = AlphaBlockBreak.GetInstance().getEconomy();
                                        }
                                        if (economy.getBalance(shopOwner) > price) {
                                            economy.withdrawPlayer(shopOwner, price);
                                            economy.depositPlayer(player, price);
                                            if (item.hasItemMeta() && item.getItemMeta().getDisplayName() != null) {
                                                player.sendMessage(prefix + "§7You succesfully sold §a" + amountToAddToSeller + "x " + item.getItemMeta().getDisplayName() + " §7to §a" + shopOwner.getName());
                                            } else {
                                                player.sendMessage(prefix + "§7You succesfully sold §a" + amountToAddToSeller + "x " + item.getType().toString().replace("_", " ") + " §7to §a" + shopOwner.getName());
                                            }
                                            addItemToVault(shopOwner.getUniqueId(), player, item, amountToAddToSeller);
                                        } else {
                                            player.sendMessage(prefix + "Shop Owner does not have enough money");
                                        }
                                    } else {
                                        player.sendMessage(prefix + "§cInternal Error. Contact an administrator on /Discord");
                                    }
                                }
                            } else {
                                player.sendMessage(prefix + "§cYou don't have enough item to sell");
                            }
                        }
                    }
                } else if (event.getView().getTitle().equals("§e§lAUCTION §f| §7Vault")) {
                    event.setCancelled(true);
                    ItemStack clickedItem = event.getCurrentItem();
                    Player player = (Player) event.getWhoClicked();
                    if (clickedItem != null && !clickedItem.getType().equals(Material.AIR)) {
                        int amountToWithdraw = 0;
                        boolean withdrawAll = false;
                        if (event.getClick().isLeftClick()) {
                            amountToWithdraw = 1;
                        } else if (event.getClick().isRightClick()) {
                            amountToWithdraw = 10;
                        } else if (event.getAction().equals(InventoryAction.DROP_ONE_SLOT)) {
                            withdrawAll = true;
                        }
                        if (withdrawAll) {
                            withdrawItemToVault(player.getUniqueId(), event.getSlot(), 0, true);
                            openVault(player);
                        } else if (amountToWithdraw > 0) {
                            withdrawItemToVault(player.getUniqueId(), event.getSlot(), amountToWithdraw, false);
                            openVault(player);
                        }
                    }
                }
            }
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("Ah")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (args.length == 2 && args[0].equalsIgnoreCase("buy")) {
                    if (player.getInventory().getItemInMainHand() != null && !player.getInventory().getItemInMainHand().getType().equals(Material.AIR)) {
                        try {
                            if (Long.parseLong(args[1]) < 1000000000000000000L) {
                                addItem(player, player.getInventory().getItemInMainHand().clone(), Long.parseLong(args[1]));
                            } else {
                                player.sendMessage(prefix + "§cPrice too high");
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else if (args.length > 0) {
                    player.sendMessage(prefix + "§cCorrect usage: /ah buy [price]");
                } else {
                    openAuctionHouse(player);
                }
            }
        }

        return true;
    }
}