package it.masterzen.Robot;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import it.masterzen.blockbreak.XMaterial;
import org.apache.commons.lang3.EnumUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class GUI implements Listener {

    private it.masterzen.Robot.Main main;
    private final String prefix = "§e§lROBOTS §8»§7 ";

    private List<UUID> guiList = new ArrayList<>();

    public GUI(it.masterzen.Robot.Main main) {
        this.main = main;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lROBOTS §f| §7Factory")) {
                event.setCancelled(true);
                if (event.getSlot() == 22) {
                    double money = getAmount(player, "Money", false);
                    double tokens = getAmount(player, "Tokens", false);
                    double xp = getAmount(player, "Xp", false);
                    if ((money > 0 && tokens > 0) || xp > 0) {

                        String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                        if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                            Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                            if (playerClass == Enums.Classes.MERCHANT) {
                                money = money * playerClass.getBooster();
                            } else if (playerClass == Enums.Classes.MINER) {
                                tokens = tokens * playerClass.getBooster();
                            } else if (playerClass == Enums.Classes.EXPERIENCER) {
                                xp = xp * playerClass.getBooster();
                            }
                        }

                        main.mainClass.getEconomy().depositPlayer(player, money);
                        main.mainClass.getResume().addValue(player, "Money", money);
                        main.mainClass.getTeAPI().addTokens(player, tokens);
                        main.mainClass.getResume().addValue(player, "Tokens", tokens);

                        player.sendMessage(prefix + "You received §a§l" + main.mainClass.newFormatNumber(money, player) + "§7 Money");
                        player.sendMessage(prefix + "You received §a§l" + main.mainClass.newFormatNumber(tokens, player) + "§7 Tokens");
                        if (xp > 0) {
                            player.giveExp((int) xp);
                            player.sendMessage(prefix + "You received §a§l" + main.mainClass.newFormatNumber(xp, player) + "§7 Xp");
                        }

                        main.updateLastUse(player);
                        openGUI(player);
                    }
                }
            }
        }
    }

    public double getAmount(Player player, String what, boolean onlyGains) {
        long tier1Amount = main.getPoints().get(player.getUniqueId()).getTier1Amount();
        long tier2Amount = main.getPoints().get(player.getUniqueId()).getTier2Amount();
        long tier3Amount = main.getPoints().get(player.getUniqueId()).getTier3Amount();
        long tier4Amount = main.getPoints().get(player.getUniqueId()).getTier4Amount();
        long tier5Amount = main.getPoints().get(player.getUniqueId()).getTier5Amount();
        long tier6Amount = main.getPoints().get(player.getUniqueId()).getTier6Amount();
        long tier7Amount = main.getPoints().get(player.getUniqueId()).getTier7Amount();
        long tier8Amount = main.getPoints().get(player.getUniqueId()).getTier8Amount();
        long tier9Amount = main.getPoints().get(player.getUniqueId()).getTier9Amount();
        long tier10Amount = main.getPoints().get(player.getUniqueId()).getTier10Amount();
        double amount = 0;

        if (what.equalsIgnoreCase("Money")) {
            amount = amount + (tier1Amount * 10000000000D);
            amount = amount + (tier2Amount * 20000000000D);
            amount = amount + (tier3Amount * 30000000000D);
            amount = amount + (tier4Amount * 40000000000D);
            amount = amount + (tier5Amount * 50000000000D);
            amount = amount + (tier6Amount * 60000000000D);
            amount = amount + (tier7Amount * 70000000000D);
            amount = amount + (tier8Amount * 80000000000D);
            amount = amount + (tier9Amount * 90000000000D);
            amount = amount + (tier10Amount * 150000000000D);
        } else if (what.equalsIgnoreCase("Tokens")) {
            amount = amount + (tier1Amount * 10000);
            amount = amount + (tier2Amount * 20000);
            amount = amount + (tier3Amount * 30000);
            amount = amount + (tier4Amount * 40000);
            amount = amount + (tier5Amount * 50000);
            amount = amount + (tier6Amount * 60000);
            amount = amount + (tier7Amount * 70000);
            amount = amount + (tier8Amount * 80000);
            amount = amount + (tier9Amount * 90000);
            amount = amount + (tier10Amount * 150000);
        } /*else if (what.equalsIgnoreCase("Xp")) {
            int xpLevel = main.mainClass.getMaxLevelPex(player, "xplevel.");
            if (xpLevel > 0) {
                amount = amount + (tier10Amount * (10 + (5 * (xpLevel + 1))) * 5);
            }
        }*/

        if (!onlyGains) {
            long difference = Duration.between(main.getLastUse(player), LocalDateTime.now()).getSeconds();
            amount = amount * difference;
        }

        return amount;
    }

    public boolean haveRobots(Player player) {
        if (!main.getPoints().containsKey(player.getUniqueId())) {
            return false;
        } else {
            if (main.getPoints().get(player.getUniqueId()).getTier1Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier2Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier3Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier4Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier5Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier6Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier7Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier8Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier9Amount() > 0 ||
                main.getPoints().get(player.getUniqueId()).getTier10Amount() > 0
            ) {
                return true;
            } else {
                return false;
            }
        }
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§e§lROBOTS §f| §7Factory");
        it.masterzen.commands.Main.FillBorder(gui);

        boolean haveRobots = haveRobots(player);
        boolean startLoop = false;
        if (haveRobots) {
            long tier1Amount = main.getPoints().get(player.getUniqueId()).getTier1Amount();
            long tier2Amount = main.getPoints().get(player.getUniqueId()).getTier2Amount();
            long tier3Amount = main.getPoints().get(player.getUniqueId()).getTier3Amount();
            long tier4Amount = main.getPoints().get(player.getUniqueId()).getTier4Amount();
            long tier5Amount = main.getPoints().get(player.getUniqueId()).getTier5Amount();
            long tier6Amount = main.getPoints().get(player.getUniqueId()).getTier6Amount();
            long tier7Amount = main.getPoints().get(player.getUniqueId()).getTier7Amount();
            long tier8Amount = main.getPoints().get(player.getUniqueId()).getTier8Amount();
            long tier9Amount = main.getPoints().get(player.getUniqueId()).getTier9Amount();
            long tier10Amount = main.getPoints().get(player.getUniqueId()).getTier10Amount();

            ItemStack item = XMaterial.ZOMBIE_SPAWN_EGG.parseItem();
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§6§lFACTORY");
            List<String> lore = new ArrayList<>();
            lore.add("");
            lore.add("§e§lROBOT AMOUNT");
            lore.add("§e| §e" + tier1Amount + " §fTier 1");
            lore.add("§e| §e" + tier2Amount + " §fTier 2");
            lore.add("§e| §e" + tier3Amount + " §fTier 3");
            lore.add("§e| §e" + tier4Amount + " §fTier 4");
            lore.add("§e| §e" + tier5Amount + " §fTier 5");
            lore.add("§e| §e" + tier6Amount + " §fTier 6");
            lore.add("§e| §e" + tier7Amount + " §fTier 7");
            lore.add("§e| §e" + tier8Amount + " §fTier 8");
            lore.add("§e| §e" + tier9Amount + " §fTier 9");
            lore.add("§e| §e" + tier10Amount + " §fTier 10");
            lore.add("");
            lore.add("§6§lGAINS §7§o( every second )");
            lore.add("§6| §6" + main.mainClass.newFormatNumber(getAmount(player, "Money", true), player) + "§f Money");
            lore.add("§6| §6" + main.mainClass.newFormatNumber(getAmount(player, "Tokens", true), player) + "§f Tokens");
            //lore.add("  §a" + main.mainClass.newFormatNumber(getAmount(player, "Xp", true)) + "§7 Xp");
            lore.add("");
            lore.add("§7§lSTATS");
            lore.add("§7| §7" + main.mainClass.newFormatNumber(getAmount(player, "Money", false), player) + "§f Money");
            lore.add("§7| §7" + main.mainClass.newFormatNumber(getAmount(player, "Tokens", false), player) + "§f Tokens");
            //lore.add("  §b" + main.mainClass.newFormatNumber(getAmount(player, "Xp", false)) + "§7 Xp");
            lore.add("");
            lore.add("§6§lUSAGE");
            lore.add("§6| §fClick to collect");
            meta.setLore(lore);
            item.setItemMeta(meta);

            gui.setItem(22, item);
            startLoop = true;
        } else {
            ItemStack item = XMaterial.ZOMBIE_SPAWN_EGG.parseItem();
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§c§lROBOTS");
            List<String> lore = new ArrayList<>();
            lore.add("");
            lore.add("§c§lERROR");
            lore.add("§c| §fYou don't have any Robot !");
            lore.add("");
            lore.add("§7§lDESCRIPTION");
            lore.add("§7| §fYou can get them from §7Robot Finder Enchant");
            lore.add("§7| §for from §7Random Keys");
            meta.setLore(lore);
            item.setItemMeta(meta);

            gui.setItem(22, item);
        }

        player.openInventory(gui);
        if (startLoop) {
            updateInventory(player);
        }
    }

    public void updateInventory(Player player) {
        if (!guiList.contains(player.getUniqueId())) {
            guiList.add(player.getUniqueId());

            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline() && player.getOpenInventory() != null && player.getOpenInventory().getTitle().equals("§e§lROBOTS §f| §7Factory") && guiList.contains(player.getUniqueId())) {
                        openGUI(player);
                    } else {
                        guiList.remove(player.getUniqueId());
                        this.cancel();
                    }
                }
            }.runTaskTimer(main.mainClass, 250L, 250L);
        }
    }

}
