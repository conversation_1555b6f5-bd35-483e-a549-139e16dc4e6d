package it.masterzen.commands;

import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.Resume.Resume;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import it.masterzen.blockbreak.XMaterial;
import it.masterzen.blockbreak.XPManager;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import net.luckperms.api.node.types.PermissionNode;
import net.milkbowl.vault.economy.Economy;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class XPShop implements Listener {

    private AlphaBlockBreak getVariable;

    public XPShop(AlphaBlockBreak plugin) {
        this.getVariable = plugin;
        economy = getVariable.getEconomy();
        teAPI = getVariable.getTeAPI();
        LuckPerms = getVariable.getLuckPerms();
        resume = getVariable.getResume();
    }

    private final String prefix = "§e§lXPSHOP §8»§7 ";
    protected Player player;

    private TokenEnchantAPI teAPI;
    private Economy economy;
    private net.luckperms.api.LuckPerms LuckPerms;
    private Resume resume;

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws InstantiationException, IllegalAccessException {
        if (event.getView().getTitle().equals("§e§lXP §f| §7Shop")) {

            player = (Player) event.getWhoClicked();
            ItemStack clickedItem = event.getCurrentItem();

            if (clickedItem == null || event.getSlot() != 20 || event.getSlot() != 22 || event.getSlot() != 24) {
                event.setCancelled(true);
            }

            if (event.getSlot() == 12) {
                addQuests(player);
            } else if (event.getSlot() == 14) {
                xpLevel(player);
            }
            /*if (event.getSlot() == 20) {
                giveMoney(player, GetCurrentAmount(player, "Money"));
            } else if (event.getSlot() == 22) {
                xpLevel(player);
            } else if (event.getSlot() == 24) {
                giveTokens(player, GetCurrentAmount(player, "Tokens"));
            }*/
        }
    }

    private void xpLevel(Player player) {
        int XP = player.getLevel();

        int Level = 0;

        User user = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        for (Node perm : user.getDistinctNodes()) {
            if (perm.getKey().contains("xplevel.")) {
                Level = Integer.parseInt(perm.getKey().replace("xplevel.", ""));
            }
        }

        if (XP >= (150 + (100 * Level))) {
            Node oldLevel = Node.builder("xplevel." + Level).build();
            Node newLevel = Node.builder("xplevel." + (Level + 1)).build();
            user.data().remove(oldLevel);
            user.data().add(newLevel);
            player.setLevel((player.getLevel() - (150 + (100 * Level))));
            getVariable.getLuckPerms().getUserManager().saveUser(user);
            player.sendMessage(prefix + "§aYou successfully Level UP");
            XPShopInventory(player);
        } else {
            player.sendMessage(prefix + "§cYou don't have enough XP Levels");
        }
    }

    private void giveMoney(Player player, Pair<Double, Double> money) throws IllegalAccessException, InstantiationException {
        if (player.getTotalExperience() < 0) {
            return;
        }
        if (money.getKey() == 0) {
            player.sendMessage(prefix + "§cYou don't have any XP to exchange");
            return;
        }

        double maxRange = money.getValue() - money.getKey();
        double randomMoney = ThreadLocalRandom.current().nextDouble(maxRange) + money.getKey();
        getVariable.getEconomy().depositPlayer(player, randomMoney);
        resume.addValue(player, "Money", randomMoney);
        player.setTotalExperience(0);
        player.setLevel(0);
        player.setExp(0);
        player.sendMessage(prefix + "§7You successfully exchange a total of §a§l" + getVariable.newFormatNumber(randomMoney, player) + " §7Money");
        XPShopInventory(player);
    }

    private void giveTokens(Player player, Pair<Double, Double> tokens) throws IllegalAccessException, InstantiationException {
        if (player.getTotalExperience() < 0) {
            return;
        }
        if (tokens.getKey() == 0) {
            player.sendMessage(prefix + "§cYou don't have any XP to exchange");
            return;
        }

        double maxRange = tokens.getValue() - tokens.getKey();
        double randomTokens = ThreadLocalRandom.current().nextDouble(maxRange) + tokens.getKey();
        getVariable.getTeAPI().addTokens(player, randomTokens);
        resume.addValue(player, "Tokens", randomTokens);
        player.setTotalExperience(0);
        player.setLevel(0);
        player.setExp(0);
        player.sendMessage(prefix + "§7You successfully exchange a total of §a§l" + getVariable.newFormatNumber(randomTokens, player) + " §7Tokens");
        XPShopInventory(player);
    }

    private void addQuests(Player player) {
        PlayerData data = getVariable.getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getXpShopQuestClaimedOfDay() == null) {
            data.setXpShopQuestClaimedOfDay(0);
        }

        if (data.getXpShopFirstClaimOfDay() != null && new Date().after(DateUtils.addDays(data.getXpShopFirstClaimOfDay(), 1))) {
            data.setXpShopFirstClaimOfDay(new Date());
            data.setXpShopQuestClaimedOfDay(0);
        }

        int totalXP = XPManager.getTotalExperience(player);
        int price = (100000000 * (data.getXpShopQuestClaimedOfDay() + 1));
        if (totalXP >= price) {

            if (data.getXpShopFirstClaimOfDay() == null) {
                data.setXpShopFirstClaimOfDay(new Date());
            }

            data.setXpShopQuestClaimedOfDay(data.getXpShopQuestClaimedOfDay() + 1);
            getVariable.getQuestManager().addQuest(player);
            XPManager.setTotalExperience(player, totalXP - price);
            player.sendMessage(prefix + "§aYou received a random quests");
        } else {
            player.sendMessage(prefix + "§cYou don't have enough XP");
        }
    }

    public void XPShopInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lXP §f| §7Shop");
        Main.FillBorder(gui);
        player.updateInventory();

        Pair<Double, Double> moneyRange = GetCurrentAmount(player, "Money");
        Pair<Double, Double> tokensRange = GetCurrentAmount(player, "Tokens");
        ItemMeta meta;

        List<String> Lore = new ArrayList<>();
        ItemStack Emerald = XMaterial.EMERALD.parseItem();
        assert Emerald != null;
        meta = Emerald.getItemMeta();
        meta.setDisplayName("§6§lMONEY §f| §eExchange");
        Lore.add("");
        Lore.add("§e§lCURRENT WITHDRAW");
        Lore.add("§e| §fMin value: §e" + getVariable.newFormatNumber(moneyRange.getKey(), player));
        Lore.add("§e| §fMax value: §e" + getVariable.newFormatNumber(moneyRange.getValue(), player));
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fThe rewards are based on the amount of XP you have.");
        Lore.add("§7| §fYou can acquire boosters by the /class EXPERIENCER");
        Lore.add("§7| §for from the shop booster. Give a look at the book in this GUI");
        meta.setLore(Lore);
        Emerald.setItemMeta(meta);

        Lore = new ArrayList<>();
        ItemStack Magma_Cream = XMaterial.MAGMA_CREAM.parseItem();
        assert Magma_Cream != null;
        meta = Magma_Cream.getItemMeta();
        meta.setDisplayName("§2§lTOKENS §f| §aExchange");
        Lore.add("");
        Lore.add("§e§lCURRENT WITHDRAW");
        Lore.add("§e| §fMin value: §e" + getVariable.newFormatNumber(tokensRange.getKey(), player));
        Lore.add("§e| §fMax value: §e" + getVariable.newFormatNumber(tokensRange.getValue(), player));
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fThe rewards are based on the amount of XP you have.");
        Lore.add("§7| §fYou can acquire boosters by the /class EXPERIENCER");
        Lore.add("§7| §for from the shop booster. Give a look at the book in this GUI");
        meta.setLore(Lore);
        Magma_Cream.setItemMeta(meta);

        PlayerData data = getVariable.getMongoReader().getPlayerData(player.getUniqueId());

        int questClaimed = (data.getXpShopQuestClaimedOfDay() == null ? 0 : data.getXpShopQuestClaimedOfDay()) + 1;
        if (new Date().after(DateUtils.addDays((data.getXpShopFirstClaimOfDay() == null ? new Date() : data.getXpShopFirstClaimOfDay()), 1))) {
            questClaimed = 1;
        }
        ItemStack randomQuest = new ItemStack(Material.PAPER);
        meta = randomQuest.getItemMeta();
        meta.setDisplayName("§6§lQUEST §f| §aExchange");
        Lore = new ArrayList<>();
        Lore.add("");
        Lore.add("§e§lREWARD");
        Lore.add("§e| §fPrice: §e" + getVariable.newFormatNumber(100000000 * questClaimed, player) + " XP");
        Lore.add("§e| §fNext price reset: §e" + (data.getXpShopFirstClaimOfDay() == null ? "N.A." : DateUtils.addDays(data.getXpShopFirstClaimOfDay(), 1)));
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fYou will get a Random Quest.");
        Lore.add("§7| §fEvery quest will increase the price of 100M");
        Lore.add("§7| §f24h after that you buy the first quest");
        Lore.add("§7| §fthe price will reset");
        Lore.add("");
        meta.setLore(Lore);
        randomQuest.setItemMeta(meta);

        Lore = new ArrayList<>();
        int Level = 0;

        User user = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        for (Node perm : user.getDistinctNodes()) {
            if (perm.getKey().contains("xplevel.")) {
                Level = Integer.parseInt(perm.getKey().replace("xplevel.", ""));
            }
        }

        ItemStack XPBottle = XMaterial.EXPERIENCE_BOTTLE.parseItem();
        assert XPBottle != null;
        meta = XPBottle.getItemMeta();

        meta.setDisplayName("§2§lLEVEL UP §f| §aClick me");
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e" + (150 + (100 * Level)) + " §fXP Level");
        Lore.add("");
        Lore.add("§7§lNEXT LEVEL STATS");
        Lore.add("§7| §fProc Chance: §7" + (50 + ((Level + 1))));
        Lore.add("§7| §fXP Amount: §7" + (10 + (5 * (Level + 1))));
        Lore.add("");

        meta.setLore(Lore);
        XPBottle.setItemMeta(meta);

        Integer xpShopBooster = data.getXpShopBooster();
        if (xpShopBooster == null) {
            xpShopBooster = 0;
        }

        ItemStack informations = XMaterial.BOOK.parseItem();
        meta = informations.getItemMeta();
        meta.setDisplayName("§6§lINFORMATION");
        Lore = new ArrayList<>();
        Lore.add("");
        Lore.add("§7§lREWARDS BOOSTER");
        Lore.add("§7| §7" + xpShopBooster + "%");
        Lore.add("");
        Lore.add("§7§lDESCRIPTION");
        Lore.add("§7| §fEvery hour if you have mined at least 10k blocks");
        Lore.add("§7| §fYou will receive an additional §72% §fbooster");
        Lore.add("");
        meta.setLore(Lore);
        informations.setItemMeta(meta);

        gui.setItem(4, informations);
        gui.setItem(12, randomQuest);
        //gui.setItem(20, Emerald);
        gui.setItem(14, XPBottle);
        //gui.setItem(24, Magma_Cream);

        player.openInventory(gui);
    }

    private Pair<Double, Double> GetCurrentAmount(Player player, String Type) {
        long PrestigeLevel = 0;
        double Amount = 0;
        double XP = getTotalExperience(player);
        double Price = 15000;
        String tmp;

        //User user = LuckPerms.getPlayerAdapter(Player.class).getUser(player);
        CachedMetaData metaData = getVariable.getLuckPerms().getPlayerAdapter(Player.class).getMetaData(player);
        String prefix = metaData.getPrefix();
        assert prefix != null;
        if (prefix.contains("Lv")) {
            tmp = prefix.replace("§bLv ", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            PrestigeLevel = Long.parseLong(tmp);
        }

        if (Type.equals("Money")) {
            if (PrestigeLevel > 0) {
                Amount = (((XP * PrestigeLevel * 5) * Price) * 30) * getVariable.getSeasonDay(); //5
            } else {
                Amount = ((XP * Price) * 10);
            }
            //Amount = AlphaBlockBreak.GetInstance().getEconomy().getBalance(player) / 50000 * (XP / 1500);
        } else if (Type.equals("Tokens")) {
            if (PrestigeLevel > 0) {
                Amount = (XP * PrestigeLevel * 5) * 0.005 * getVariable.getSeasonDay();// / 1.5;
            } else {
                Amount = XP * 0.005;// / 1.5;
            }
            //Amount = AlphaBlockBreak.GetInstance().getPickValue(player) / 100000 * (XP / 150);
        }

        String playerClassString = getVariable.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
        if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
            Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
            if (playerClass == Enums.Classes.MERCHANT && Type.equals("Money")) {
                Amount = Amount * playerClass.getBooster();
            } else if (playerClass == Enums.Classes.MINER && Type.equals("Tokens")) {
                Amount = Amount * playerClass.getBooster();
            }
        }

        PlayerData data = getVariable.getMongoReader().getPlayerData(player.getUniqueId());
        Integer xpShopBooster = data.getXpShopBooster();
        if (xpShopBooster != null && xpShopBooster > 0) {
            Amount = Amount + (Amount * (0.01 * xpShopBooster));
        }

        return new ImmutablePair<>(Amount / 2, Amount * 2);
    }

    //This method is used to update both the recorded total experience and displayed total experience.
    //We reset both types to prevent issues.
    public static void setTotalExperience(final Player player, final int exp)
    {
        if (exp < 0)
        {
            throw new IllegalArgumentException("Experience is negative!");
        }
        player.setExp(0);
        player.setLevel(0);
        player.setTotalExperience(0);

        //This following code is technically redundant now, as bukkit now calulcates levels more or less correctly
        //At larger numbers however... player.getExp(3000), only seems to give 2999, putting the below calculations off.
        int amount = exp;
        while (amount > 0)
        {
            final int expToLevel = getExpAtLevel(player);
            amount -= expToLevel;
            if (amount >= 0)
            {
                // give until next level
                player.giveExp(expToLevel);
            }
            else
            {
                // give the rest
                amount += expToLevel;
                player.giveExp(amount);
                amount = 0;
            }
        }
    }

    private static int getExpAtLevel(final Player player)
    {
        return getExpAtLevel(player.getLevel());
    }

    public static int getExpAtLevel(final int level)
    {
        if (level > 29)
        {
            return 62 + (level - 30) * 7;
        }
        if (level > 15)
        {
            return 17 + (level - 15) * 3;
        }
        return 17;
    }

    public static int getExpToLevel(final int level)
    {
        int currentLevel = 0;
        int exp = 0;

        while (currentLevel < level)
        {
            exp += getExpAtLevel(currentLevel);
            currentLevel++;
        }
        if (exp < 0)
        {
            exp = Integer.MAX_VALUE;
        }
        return exp;
    }

    //This method is required because the bukkit player.getTotalExperience() method, shows exp that has been 'spent'.
    //Without this people would be able to use exp and then still sell it.
    public static long getTotalExperience(final Player player)
    {
        long exp = Math.round(getExpAtLevel(player) * player.getExp());
        int currentLevel = player.getLevel();

        while (currentLevel > 0)
        {
            currentLevel--;
            exp += getExpAtLevel(currentLevel);
        }
        if (exp < 0)
        {
            exp = Long.MAX_VALUE;
        }
        return exp;
    }

    public static int getExpUntilNextLevel(final Player player)
    {
        int exp = (int)Math.round(getExpAtLevel(player) * player.getExp());
        int nextLevel = player.getLevel();
        return getExpAtLevel(nextLevel) - exp;
    }

}
