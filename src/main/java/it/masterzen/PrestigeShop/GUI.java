package it.masterzen.PrestigeShop;

import com.sk89q.worldedit.MaxChangedBlocksException;
import com.vk2gpz.tokenenchant.api.TokenEnchantAPI;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.Enums;
import it.masterzen.blockbreak.XMaterial;
import net.milkbowl.vault.economy.Economy;
import org.apache.commons.lang3.EnumUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.RegisteredServiceProvider;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

public class GUI implements Listener {

    private Main main;
    private it.masterzen.Keys.Main keyManager;
    private TokenEnchantAPI teAPI;
    private Economy economy;
    private it.masterzen.Withdraw.Main withdrawManager;
    private final String prefix = "§e§lPRESTIGE POINTS §8»§7 ";

    public GUI(Main main) {
        this.main = main;
        keyManager = main.mainClass.getKeysManager();
        withdrawManager = new it.masterzen.Withdraw.Main(main.mainClass);

        teAPI = TokenEnchantAPI.getInstance();
        RegisteredServiceProvider<Economy> rsp = main.mainClass.getServer().getServicesManager().getRegistration(Economy.class);
        economy = rsp.getProvider();
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lPRESTIGE POINTS §f| §7Shop")) {
                event.setCancelled(true);

            /*
            gui.setItem(20, tokens);
            gui.setItem(21, money);
            gui.setItem(23, robots);
            gui.setItem(24, diamondSword);
            gui.setItem(30, silverfishSpawner);
            gui.setItem(31, omegaKeys);
            gui.setItem(32, vip);
            gui.setItem(49, points);
             */

                if (event.getSlot() == 20) {
                    if (event.isLeftClick()) {
                        giveTokens(player, false);
                    } else if (event.isRightClick()) {
                        giveTokens(player, true);
                    }
                } else if (event.getSlot() == 21) {
                    if (event.isLeftClick()) {
                        giveMoney(player, false);
                    } else if (event.isRightClick()) {
                        giveMoney(player, true);
                    }
                } else if (event.getSlot() == 22) {
                    if (event.isLeftClick()) {
                        giveBeaconBooster(player, false);
                    } else if (event.isRightClick()) {
                        giveBeaconBooster(player, true);
                    }
                } else if (event.getSlot() == 23) {
                    if (event.isLeftClick()) {
                        giveRobots(player, false);
                    } else if (event.isRightClick()) {
                        giveRobots(player, true);
                    }
                } else if (event.getSlot() == 29) {
                    if (event.isLeftClick()) {
                        giveOmegaKeys(player, false);
                    } else if (event.isRightClick()) {
                        giveOmegaKeys(player, true);
                    }
                } else if (event.getSlot() == 30) {
                    if (event.isLeftClick()) {
                        giveTokenKeys(player, false);
                    } else if (event.isRightClick()) {
                        giveTokenKeys(player, true);
                    }
                } else if (event.getSlot() == 24) {
                    if (event.isLeftClick()) {
                        giveSharpness10(player, false);
                    } else if (event.isRightClick()) {
                        giveSharpness10(player, true);
                    }
                } else if (event.getSlot() == 32) {
                    if (event.isLeftClick()) {
                        giveSpawner(player, false);
                    } else if (event.isRightClick()) {
                        giveSpawner(player, true);
                    }
                } else if (event.getSlot() == 33) {
                    if (event.isLeftClick()) {
                        giveVip(player, false);
                    } else if (event.isRightClick()) {
                        giveVip(player, true);
                    }
                } else if (event.getSlot() == 49) {
                    addPoints(player);
                }
                openGUI(player);
            }
        }
    }

    public void addPoints(Player player) {
        if (!player.hasPermission("prestigeshop.cooldown") || player.isOp()) {
            long playerLevel = main.mainClass.getPlayerLevel(player);
            if (player.isOp()) {
                playerLevel = 10000;
            }

            main.addPrestigePoints(player, playerLevel);

            // Reset Omega key price to base (5000) when claiming points
            PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            data.setOmegaKeyCurrentPrice(5000);
            main.mainClass.getMongoReader().savePlayerData(data, false);

            main.mainClass.addPex(player, "prestigeshop.cooldown", 43200, true);
        } else {
            player.sendMessage(prefix + "§cCommand under cooldown");
        }
    }

    public void giveTokens(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 1000) {
            if (max) {
                int iterations = 0;

                double tokens = getPlayerStats(player, "Tokens");
                double totalTokens = 0;
                while (playerPoints >= 1000) {
                    totalTokens = totalTokens + tokens;
                    playerPoints = playerPoints - 1000;
                    iterations++;
                }

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MINER) {
                        totalTokens = totalTokens * playerClass.getBooster();
                    }
                }

                teAPI.addTokens(player, totalTokens);
                main.removePrestigePoints(player, iterations * 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalTokens, player) + " §7Tokens");
            } else {
                double tokens = getPlayerStats(player, "Tokens");

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MINER) {
                        tokens = tokens * playerClass.getBooster();
                    }
                }

                teAPI.addTokens(player, tokens);
                main.removePrestigePoints(player, 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(tokens, player) + " §7Tokens");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveMoney(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 1000) {
            if (max) {
                int iterations = 0;

                double money = getPlayerStats(player, "Money");
                double totalMoney = 0;
                while (playerPoints >= 1000) {
                    totalMoney = totalMoney + money;
                    playerPoints = playerPoints - 1000;
                    iterations++;
                }

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MERCHANT) {
                        totalMoney = totalMoney * playerClass.getBooster();
                    }
                }

                economy.depositPlayer(player, totalMoney);
                main.removePrestigePoints(player, iterations * 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalMoney, player) + " §7Money");
            } else {
                double money = getPlayerStats(player, "Money");

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.MERCHANT) {
                        money = money * playerClass.getBooster();
                    }
                }

                economy.depositPlayer(player, money);
                main.removePrestigePoints(player, 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(money, player) + " §7Money");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveRobots(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 5000) {
            if (max) {
                int iterations = 0;

                int totalRobots = 0;
                while (playerPoints >= 5000) {
                    totalRobots = totalRobots + 1;
                    playerPoints = playerPoints - 5000;
                    iterations++;
                }

                main.mainClass.getRobotSystem().addPoints(player, 6, totalRobots);
                //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "robotadmin give " + player.getName() + " " + totalRobots);
                main.removePrestigePoints(player, iterations * 5000L);
                player.sendMessage(prefix + "You received a total of §a§l" + totalRobots + " §7Tier 6 Robots");
            } else {
                main.mainClass.getRobotSystem().addPoints(player, 6, 1);
                //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "robotadmin give " + player.getName() + " " + 250);
                main.removePrestigePoints(player, 5000L);
                player.sendMessage(prefix + "You received §a§l1 §7Tier 6 Robot");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveBeaconBooster(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 50000) {
            if (max) {
                int iterations = 0;

                while (playerPoints >= 50000) {
                    playerPoints = playerPoints - 50000;
                    iterations++;
                }

                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission settemp beacon.double true " + (5 * iterations) +"m accumulate");
                main.removePrestigePoints(player, iterations * 50000L);
                player.sendMessage(prefix + "You received a total of §a§l" + (5 * iterations) + " §7Minutes of Beacon Boosters");
            } else {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "lp user " + player.getName() + " permission settemp beacon.double true 5m accumulate");
                main.removePrestigePoints(player, 50000L);
                player.sendMessage(prefix + "You received §a§l5 §7Minutes of Beacon Boosters");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveOmegaKeys(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);

        // Load player-specific current Omega key price from DB (defaults to 5000)
        PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
        int currentPrice = data.getOmegaKeyCurrentPrice();

        if (playerPoints >= currentPrice) {
            if (max) {
                int totalKeys = 0;
                long totalSpent = 0L;
                int price = currentPrice;

                // Buy as many as possible with increasing price +1000 each purchase
                while (playerPoints >= price) {
                    totalKeys++;
                    playerPoints -= price;
                    totalSpent += price;
                    price += 1000;
                }

                // Class booster for keys amount (if any)
                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.TREASURER) {
                        totalKeys = (int) (totalKeys * playerClass.getBooster());
                    }
                }

                if (totalKeys > 0) {
                    keyManager.giveKeys(player, "Omega", totalKeys, false);
                    main.removePrestigePoints(player, totalSpent);

                    // Persist the new next price
                    data.setOmegaKeyCurrentPrice(price);
                    main.mainClass.getMongoReader().savePlayerData(data, false);

                    player.sendMessage(prefix + "You received a total of §a§l" + totalKeys + " " + keyManager.getKeyName("Omega") + " §7Keys");
                } else {
                    player.sendMessage(prefix + "§cYou don't have enough Points");
                }
            } else {
                int totalKeys = 1;

                // Class booster for keys amount (if any)
                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.TREASURER) {
                        totalKeys = (int) (totalKeys * playerClass.getBooster());
                    }
                }

                keyManager.giveKeys(player, "Omega", totalKeys, false);
                main.removePrestigePoints(player, (long) currentPrice);

                // Increase and persist next price (+1000)
                data.setOmegaKeyCurrentPrice(currentPrice + 1000);
                main.mainClass.getMongoReader().savePlayerData(data, false);

                player.sendMessage(prefix + "You received §a§l1 " + keyManager.getKeyName("Omega") + " §7Key");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveTokenKeys(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 1000) {
            if (max) {
                int iterations = 0;

                int totalKeys = 0;
                while (playerPoints >= 2500) {
                    totalKeys = totalKeys + 1;
                    playerPoints = playerPoints - 2500;
                    iterations++;
                }

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.TREASURER) {
                        totalKeys = (int) (totalKeys * playerClass.getBooster());
                    }
                }

                keyManager.giveKeys(player, "Token", totalKeys, false);
                main.removePrestigePoints(player, iterations * 2500L);
                player.sendMessage(prefix + "You received a total of §a§l" + totalKeys + " " + keyManager.getKeyName("Token") + " §7Keys");
            } else {
                int totalKeys = 1;

                String playerClassString = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                    Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                    if (playerClass == Enums.Classes.TREASURER) {
                        totalKeys = (int) (totalKeys * playerClass.getBooster());
                    }
                }

                keyManager.giveKeys(player, "Token", totalKeys, false);
                main.removePrestigePoints(player, 2500L);
                player.sendMessage(prefix + "You received §a§l1 " + keyManager.getKeyName("Token") + " §7Key");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveSharpness10(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 10000) {
            if (max) {
                int iterations = 0;

                int totalSword = 0;
                while (playerPoints >= 10000) {
                    totalSword = totalSword + 1;
                    playerPoints = playerPoints - 10000;
                    iterations++;
                }

                ItemStack sword = new ItemStack(Material.DIAMOND_SWORD);
                sword.setAmount(totalSword);
                sword.addUnsafeEnchantment(Enchantment.DAMAGE_ALL, 10);
                player.getInventory().addItem(sword);
                main.removePrestigePoints(player, iterations * 10000L);
                player.sendMessage(prefix + "You received a total of §a§l" + totalSword + " §7Diamond Sword ( Sharpness 10 )");
            } else {
                ItemStack sword = new ItemStack(Material.DIAMOND_SWORD);
                sword.addUnsafeEnchantment(Enchantment.DAMAGE_ALL, 10);
                player.getInventory().addItem(sword);
                main.removePrestigePoints(player, 10000L);
                player.sendMessage(prefix + "You received §a§l1 §7Diamond Sword ( Sharpness 10 )");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveSpawner(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 25000) {
            if (max) {
                int iterations = 0;

                int totalSpawners = 0;
                while (playerPoints >= 25000) {
                    totalSpawners = totalSpawners + 1;
                    playerPoints = playerPoints - 25000;
                    iterations++;
                }

                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish " + totalSpawners);
                main.removePrestigePoints(player, iterations * 25000L);
                //player.sendMessage(prefix + "You received a total of §a§l" + totalSword + " §7Diamond Sword ( Sharpness 10 )");
            } else {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish 1");
                main.removePrestigePoints(player, 25000L);
                //player.sendMessage(prefix + "You received §a§l1 §7Diamond Sword ( Sharpness 10 )");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    public void giveVip(Player player, boolean max) {
        long playerPoints = main.getPrestigePoints(player);
        if (playerPoints >= 100000) {
            if (max) {
                int iterations = 0;

                int totalVip = 0;
                while (playerPoints >= 100000) {
                    totalVip = totalVip + 1;
                    playerPoints = playerPoints - 100000;
                    iterations++;
                }

                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "voucher give " + player.getName() + " Vip " + totalVip);
                main.removePrestigePoints(player, iterations * 100000L);
                //player.sendMessage(prefix + "You received a total of §a§l" + totalSword + " §7Diamond Sword ( Sharpness 10 )");
            } else {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "voucher give " + player.getName() + " Vip 1");
                main.removePrestigePoints(player, 100000L);
                //player.sendMessage(prefix + "You received §a§l1 §7Diamond Sword ( Sharpness 10 )");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Points");
        }
    }

    private double getPlayerStats(Player player, String currency) {
        double amount = 0;

        long playerLevel = main.mainClass.getPlayerLevel(player);
        if (playerLevel == 0) {
            playerLevel = 1;
        }
        if (currency.equalsIgnoreCase("Money")) {
            amount = (playerLevel * 50000000000D);
        } else if (currency.equalsIgnoreCase("Tokens")) {
            amount = (playerLevel * 5000D);
        }

        return amount;
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lPRESTIGE POINTS §f| §7Shop");
        it.masterzen.commands.Main.FillBorder(gui);

        double dynamicTokens = getPlayerStats(player, "Tokens");
        double dynamicMoney = getPlayerStats(player, "Money");

        ItemStack tokens = new ItemStack(Material.MAGMA_CREAM);
        ItemMeta meta = tokens.getItemMeta();
        meta.setDisplayName("§e§lTokens §f| §7Dynamic Amount");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fThe §7amount §fis based on your prestige");
        lore.add("§7| §fCurrent amount: §7" + main.mainClass.newFormatNumber(dynamicTokens, player));
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        tokens.setItemMeta(meta);

        ItemStack money = new ItemStack(Material.PAPER);
        meta = money.getItemMeta();
        meta.setDisplayName("§e§lMoney §f| §7Dynamic Amount");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fThe §7amount §fis based on your prestige");
        lore.add("§7| §fCurrent amount: §7" + main.mainClass.newFormatNumber(dynamicMoney, player));
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        money.setItemMeta(meta);

        ItemStack robots = new ItemStack(Objects.requireNonNull(XMaterial.ZOMBIE_SPAWN_EGG.parseItem()));
        meta = robots.getItemMeta();
        meta.setDisplayName("§e§l1x Tier 6 §f| §7Robot");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e5.000 §fPoints");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        robots.setItemMeta(meta);

        ItemStack omegaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = omegaKeys.getItemMeta();
        meta.setDisplayName("§e§lOMEGA §f| §7Key");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        int currentOmegaPrice = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getOmegaKeyCurrentPrice();
        lore.add("§e| §e" + main.mainClass.FormatNumber((long) currentOmegaPrice) + " §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fPrice increases by §e1.000 §fafter each purchase");
        lore.add("§7| §fClaiming points will §areset §fthe price");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        omegaKeys.setItemMeta(meta);

        ItemStack tokenKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = tokenKeys.getItemMeta();
        meta.setDisplayName("§a§lTOKEN §f| §7Key");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e2.500 §fPoints");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        tokenKeys.setItemMeta(meta);

        ItemStack diamondSword = new ItemStack(Material.DIAMOND_SWORD);
        meta = diamondSword.getItemMeta();
        meta.setDisplayName("§e§lSharpness 10 §f| §7Sword");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e10.000 §fPoints");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        diamondSword.setItemMeta(meta);

        ItemStack silverfishSpawner = new ItemStack(Material.MOB_SPAWNER);
        meta = silverfishSpawner.getItemMeta();
        meta.setDisplayName("§c§lA§6§lL§e§lP§a§lH§b§lA §7Spawner - Silverfish");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e25.000 §fPoints");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        silverfishSpawner.setItemMeta(meta);

        ItemStack vip = new ItemStack(Material.PAPER);
        meta = vip.getItemMeta();
        meta.setDisplayName("§a§lVIP §f| §7Rank");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e100.000 §fPoints");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        vip.setItemMeta(meta);

        ItemStack beaconBooster = new ItemStack(Material.BEACON);
        meta = beaconBooster.getItemMeta();
        meta.setDisplayName("§b§lBEACON §f| §7Booster");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e50.000 §fPoints");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will gain double beacons");
        lore.add("§7| §ffor 5 minutes (stackable)");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        beaconBooster.setItemMeta(meta);

        long playerPoints = main.getPrestigePoints(player);
        ItemStack points = new ItemStack(Material.PAPER);
        meta = points.getItemMeta();
        meta.setDisplayName("§6§lPOINTS §7: §e§l" + playerPoints);
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to get points");
        lore.add("");
        lore.add("§7§lCOOLDOWN");
        lore.add("§7| §f12 Hours");
        meta.setLore(lore);
        points.setItemMeta(meta);

        gui.setItem(20, tokens);
        gui.setItem(21, money);
        gui.setItem(22, beaconBooster);
        gui.setItem(23, robots);
        gui.setItem(24, diamondSword);
        gui.setItem(29, omegaKeys);
        gui.setItem(30, tokenKeys);
        gui.setItem(32, silverfishSpawner);
        gui.setItem(33, vip);
        gui.setItem(49, points);

        player.openInventory(gui);
    }
}
