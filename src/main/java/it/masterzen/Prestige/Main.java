package it.masterzen.Prestige;

import it.masterzen.blockbreak.AlphaBlockBreak;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import net.luckperms.api.node.types.PrefixNode;
import org.apache.commons.lang.time.DurationFormatUtils;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

import javax.swing.*;
import javax.swing.text.html.HTMLDocument;
import java.util.Objects;

public class Main implements CommandExecutor, Listener {

    private final String prefix = "§e§lLEVELS §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    private static double a = 1D/6250;
    private static double b = 449D/5000;
    private static double c = 1849D/20;
    private static double d = -22622D;

    private double pointToMoney(long level) {
        /*long baseLevel = level;
        if (baseLevel < 250) {
            level = level + 250;
        }
        double x1 = Math.floor(level / 250) * 250;*/

        double x1 = level;
        if (level < 195) {
            d = 0;
        } else {
            d = -22622D;
        }
        double result = a * Math.pow(x1, 3) + b * Math.pow(x1, 2) + c * x1 + d;
        //result = result + (level - x1) * ((level - 1) / 250 + 1);

        result = result * 1000000000;
        /*if (result < 0) {
            result = 0;
        }
        if (baseLevel < 250) {
            result = result - 8600000000000D;
            result = result / 2; // calcola per il secondo step, quindi +2 al posto di +1
        }*/
        return result;
    }

    public double formula(long currentLevel) {
        double price = 0;

        if (currentLevel < 250) {
            price = (((double) currentLevel / 250) * 25) + 5;
        } else if (currentLevel == 250) {
            price = 29;
        } else {
            double x = (double) currentLevel / 250;
            int integerPart = (int) x;
            double y = x - integerPart;
            //mainClass.getLogger().info("y: " + y);
            //mainClass.getLogger().info("Integer: " + integerPart);
            double base = (((25 * integerPart)) + (integerPart * ((double) integerPart / 2) * 30) + 50);
            //mainClass.getLogger().info("Base: " + base);
            double increment = y * (25 * (integerPart + 1));
            //mainClass.getLogger().info("Incremento: " + increment);
            price = base + increment;
        }

        price = price * 2000000000D;
        return price;
    }

    public void addPrestige(Player player, long currentLevel, long amount, double price, boolean message) {
        mainClass.getEconomy().withdrawPlayer(player, price);

        User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
        PrefixNode tmpnode = PrefixNode.builder("§bLv " + (currentLevel) + " §7", 207).build();
        PrefixNode newLevel = PrefixNode.builder("§bLv " + (currentLevel + amount) + " §7", 207).build();
        user.data().remove(tmpnode);
        user.data().add(newLevel);
        mainClass.getLuckPerms().getUserManager().saveUser(user);

        if (message) {
            Bukkit.broadcastMessage(mainClass.getCommandPrefix() + (mainClass.getEssentials().getUser(player).getNickname() == null ? player.getName() : mainClass.getEssentials().getUser(player).getNickname()) + " §7reached §bLv " + (currentLevel + amount));
        }

        if (currentLevel + amount == 0) {
            mainClass.getLogger().warning("Prestige has been set to 0 for user " + player.getName() + ": currentLevel = " + currentLevel + ". Amount to add = " + amount);
        }
    }

    public double getPrice(long startLevel, long finalLevel) {
        /*double finalPrice = formula(startLevel);
        int iterations = 1;
        int amount = (int) (finalLevel - startLevel);

        while (amount > 0) {
            finalPrice = finalPrice + formula(startLevel + iterations);
            amount--;
            iterations++;
        }

        return finalPrice;*/

        // prezzo finale - prezzo dei livelli che già hai
        return pointToMoney(finalLevel) - pointToMoney(startLevel);
    }

    // non ritorna il numero di livelli che ti puoi permettere, ritorna il tuo numero finale di livelli
    public long getLevelYouCanReach(long currentLevel, double money) {
        money = money + pointToMoney(currentLevel); // aggiungo i soldi del livello attuale perchè faccio sempre da 0 a Livello X

        int maxIterations = 1000000;
        long totalLevelsToAdd = 0;
        while (money > 0 && maxIterations > 0) {
            maxIterations--;

            double price = pointToMoney(totalLevelsToAdd + 10000000000000L);
            if (money >= price) {
                totalLevelsToAdd = totalLevelsToAdd + 10000000000000L;
            } else {
                price = pointToMoney(totalLevelsToAdd + 1000000000000L);
                if (money >= price) {
                    totalLevelsToAdd = totalLevelsToAdd + 1000000000000L;
                } else {
                    price = pointToMoney(totalLevelsToAdd + 100000000000L);
                    if (money >= price) {
                        totalLevelsToAdd = totalLevelsToAdd + 100000000000L;
                    } else {
                        price = pointToMoney(totalLevelsToAdd + 10000000000L);
                        if (money >= price) {
                            totalLevelsToAdd = totalLevelsToAdd + 10000000000L;
                        } else {
                            price = pointToMoney(totalLevelsToAdd + 1000000000);
                            if (money >= price) {
                                totalLevelsToAdd = totalLevelsToAdd + 1000000000;
                            } else {
                                price = pointToMoney(totalLevelsToAdd + 100000000);
                                if (money >= price) {
                                    totalLevelsToAdd = totalLevelsToAdd + 100000000;
                                } else {
                                    price = pointToMoney(totalLevelsToAdd + 10000000);
                                    if (money >= price) {
                                        totalLevelsToAdd = totalLevelsToAdd + 10000000;
                                    } else {
                                        price = pointToMoney(totalLevelsToAdd + 1000000);
                                        if (money >= price) {
                                            totalLevelsToAdd = totalLevelsToAdd + 1000000;
                                        } else {
                                            price = pointToMoney(totalLevelsToAdd + 100000);
                                            if (money >= price) {
                                                totalLevelsToAdd = totalLevelsToAdd + 100000;
                                            } else {
                                                price = pointToMoney(totalLevelsToAdd + 10000);
                                                if (money >= price) {
                                                    totalLevelsToAdd = totalLevelsToAdd + 10000;
                                                } else {
                                                    price = pointToMoney(totalLevelsToAdd + 1000);
                                                    if (money >= price) {
                                                        totalLevelsToAdd = totalLevelsToAdd + 1000;
                                                    } else {
                                                        price = pointToMoney(totalLevelsToAdd + 100);
                                                        if (money >= price) {
                                                            totalLevelsToAdd = totalLevelsToAdd + 100;
                                                        } else {
                                                            price = pointToMoney(totalLevelsToAdd + 10);
                                                            if (money >= price) {
                                                                totalLevelsToAdd = totalLevelsToAdd + 10;
                                                            } else {
                                                                price = pointToMoney(totalLevelsToAdd + 1);
                                                                if (money >= price) {
                                                                    totalLevelsToAdd = totalLevelsToAdd + 1;
                                                                } else {
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return totalLevelsToAdd;
    }

    public void prestige(Player player, boolean max, int levels, boolean message) {
        long currentLevel = mainClass.getPlayerLevel(player.getUniqueId());
        if (currentLevel < 0) {
            currentLevel = 1;
        }

        //double finalPrice = formula(currentLevel + 1);
        double finalPrice = 0;
        if (mainClass.getEconomy().has(player, finalPrice) || levels > 0) {
            long iterations = 0L;
            int maxIterations = 1000000; // 1M max altrimenti si spacca tutto

            if (max) {
                long levelYouCanReach = getLevelYouCanReach(currentLevel, mainClass.getEconomy().getBalance(player));

                finalPrice = getPrice(currentLevel, levelYouCanReach);
                iterations = levelYouCanReach - currentLevel;
                if (iterations <= 0) {
                    player.sendMessage(prefix + "§cYou don't have enough money");
                    player.sendMessage("§cYou need §l" + mainClass.newFormatNumber(getPrice(currentLevel, currentLevel + 1), player));
                    return;
                }
                /*while (mainClass.getEconomy().has(player, finalPrice) && iterations < maxIterations) {
                    finalPrice = finalPrice + formula(currentLevel + iterations);
                    iterations++;
                }
                finalPrice = finalPrice - formula(currentLevel + iterations);
                iterations--;*/
            } else if (levels > 0) {
                /*maxIterations = levels;
                while (iterations < maxIterations) {
                    finalPrice = finalPrice + getPrice(currentLevel, currentLevel + iterations);
                    iterations++;
                }*/
                iterations = levels;
                finalPrice = getPrice(currentLevel, currentLevel + levels);
                if (!mainClass.getEconomy().has(player, finalPrice) && message) {
                    player.sendMessage(prefix + "§cYou don't have enough money");
                    player.sendMessage("§cYou need §l" + mainClass.newFormatNumber(finalPrice, player));
                    return;
                }
            }

            if (iterations > 0) {
                addPrestige(player, currentLevel, iterations, finalPrice, message);
            }
        } else if (message) {
            player.sendMessage(prefix + "§cYou don't have enough money");
            player.sendMessage("§cYou need §l" + mainClass.newFormatNumber(finalPrice, player));
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("prestige")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (!player.hasPermission("command.cooldown")) {
                    if (args.length == 1 && args[0].equalsIgnoreCase("max")) {
                        prestige(player, true, 0, true);
                    } else if (args.length == 1) {
                        if (mainClass.isLong(args[0])) {
                            if (Integer.parseInt(args[0]) <= 1000000) {
                                prestige(player, false, Integer.parseInt(args[0]), true);
                            } else {
                                player.sendMessage(prefix + "You can level up max 1.000.000 levels at time");
                            }
                        } else {
                            player.sendMessage(prefix + "§cPlease insert a valid number");
                        }
                    } else if (args.length == 0) {
                        prestige(player, false, 0, true);
                    } else if (args.length == 3 && args[0].equalsIgnoreCase("Price") && mainClass.isLong(args[1]) && mainClass.isLong(args[2])) {
                        if (Long.parseLong(args[2]) > Long.parseLong(args[1])) {
                            if (Long.parseLong(args[2]) - Long.parseLong(args[1]) <= 1000000) {
                                player.sendMessage(prefix + "Total price from level §a" + Long.parseLong(args[1]) + " §7to §a" + Long.parseLong(args[2]) + "§7: §a§l" + mainClass.newFormatNumber(getPrice(Long.parseLong(args[1]), Long.parseLong(args[2])), player));
                            } else {
                                player.sendMessage(prefix + "You can check price of levels for a max of 1.000.000 levels at time");
                            }
                        } else {
                            player.sendMessage(prefix + "§cSecond number must be higher then the first one");
                        }
                    } else if (args.length == 2 && args[0].equalsIgnoreCase("Price") && mainClass.isLong(args[1])) {
                        player.sendMessage(prefix + "Total price for level §a" + Long.parseLong(args[1]) + "§7: §a§l" + mainClass.newFormatNumber(formula(Long.parseLong(args[1])), player));
                    } else {
                        player.sendMessage("§e§lLEVELS §f| §7Usage");
                        player.sendMessage("    §7/prestige");
                        player.sendMessage("    §7/prestige [levels]");
                        player.sendMessage("    §7/prestige max");
                        player.sendMessage("    §7/prestige price [level]");
                        player.sendMessage("    §7/prestige price [from] [to]");
                    }
                    mainClass.addPex(player, "command.cooldown", 3, true);
                } else {
                    player.sendMessage(prefix + "Command in cooldown. Please wait");
                }
            }
        } else if (cmd.getName().equalsIgnoreCase("autoprestige")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.hasPermission("group.mvp+")) {
                    if (player.hasPermission("auto.prestige")) {
                        mainClass.removePex(player, "auto.prestige");
                        player.sendMessage(prefix + "Auto Prestige §c§lDISABLED");
                    } else {
                        mainClass.addPex(player, "auto.prestige", 0, false);
                        player.sendMessage(prefix + "Auto Prestige §a§lENABLED");
                        prestige(player, true, 0, true);
                    }
                } else {
                    player.sendMessage(prefix + "§cYou must be at least §cMVP+§c to use this command");
                    player.sendMessage("§7Buy your VIP at /buy");
                }
            }
        } else if (cmd.getName().equalsIgnoreCase("maxprestige")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                prestige(player, true, 0, true);
            }
        }
        return false;
    }
}
