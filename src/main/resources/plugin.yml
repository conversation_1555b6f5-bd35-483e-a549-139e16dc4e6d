main: it.masterzen.blockbreak.AlphaBlockBreak
name: AlphaBlockBreakEvent
version: 1.1
description: BlockBreakEvent Handler by MasterZen
api-version: "1.12.2"
depend: [Essentials, WorldEdit, WorldGuard, Multiverse-Core, TokenEnchant, Vault, LuckPerms, PrestigeMine]

commands:
  help:
    description: Open the interactive help menu
    aliases:
      - h
  XPShop:
    description: XPShop GUI
  multi:
    aliases:
      - boosters
      - multipliers
      - multiplier
    description: It returns your current AutoSell Multiplier
  Head:
    description: Give a Custom Head
  enchantGUI:
  BlockShop:
  Pet:
  pmineLevelUp:
    description: Level Up system for Private Mines
  openPMine:
    description: This will set the taxes of the player's private mine
  pickValue:
    description: Return the total value of the pickaxe
  RandomPet:
    description: Gives a random pet from /gkit
  Rebirth:
    description: Let you rebirth
  giveReactionRewards:
    description: Rewards from ChatReaction
  spawnerShop:
    aliases:
      - shard
      - shards
    description: Gui for Spawner Shard Finder Enchant
  pointShop:
    description: Gui for Exchange Points from Magic Hand stick
  giveGodsHand:
    description: Give the Magic Hand stick
  giveGodsHandLv2:
    description: Give the Magic Hand stick. Level 2
  customtag:
    description: Custom player tag command
  drop:
    description: Custom drop to prevent drop of valuable items
  giveDailyRewards:
    description: Dynamic daily rewards
  randomEvent:
    description: Random event to rise up the shop price
  dailyBoost:
    description: Send the daily boost of shop prices
  startArena:
    description: Infinite Wave System
  savePick:
    description: System to store Pickaxe into file
  givePick:
    description: Give stored pick to player
  addMultiplier:
    description: Give multiplier from Alpha Crate
  #warn:
  #  description: Warn system
  farmer:
    description: New crops feature
  giveVoteRewards:
    description: Dynamic daily rewards
  giveVotePartyRewards:
    description: Dynamic daily rewards
  rebirthShop:
    description: Rebirth Shop after 5th Rebirth
  alphaKeys:
    description: New AlphaPrison key manager
  worker:
    description: new Worker feature
  alphaEnchant:
    description: Custom sytem of enchant for Alpha Prison
  pouch:
    description: New puch function for dynamic Tokens
  beaconBackpack:
    description: New Beacon Backpack feature
  stackbeacons:
    description: Add beacons from inventory to the Backpack
  rebirthPoints:
    description: Way to add / remove rebirthPoints
  delete:
    description: Delete the item you got in your hand ( to clear un-used pickaxe )
  sg:
    description: Server Goal feature
  key:
    aliases:
      - keys
      - keyss
    description: New Key System
  resume:
    description: New resume System ( every minute )
  withdraw:
    description: Custom withdraw System ( Money, Tokens, XP )
  withdrawall:
    description: Custom withdrawAll System ( Money, Tokens, XP )
  mobcoin:
    aliases:
      - mobcoins
      - mc
    description: MobCoins System
  prestigeshop:
    aliases:
      - pshop
    description: Prestige Points System
  candy:
    aliases:
      - candys
    description: Christmas Unique System
  skill:
    aliases:
      - skills
      - st
      - skilltree
      - skillstree
    description: New Skill Tree Feature
  robot:
    aliases:
      - robots
    description: New Robot System ( converted from Skript )
  chat:
    description: Custom Chat System
  cooldown:
    aliases:
      - cd
    description: Cooldown command
  profile:
    description: Send player statistics
  fakeleave:
    description: Fake leave feature for Staffer
  giveaway:
    description: Giveaway your item to a random player
  booster:
    description: Give money, token or nuke boosters
  feature:
    description: General WiKi for Server
  armor:
    aliases:
      - armour
    description: Custom armor feature
  namemc:
    description: Handler for NameMC
  dungeon:
    aliases:
      - crystals
      - crystal
      - c
    description: New Dungeon management
  seasonday:
    description: Command used to set the current day of the season to the actual day
  discordboost:
    description: Auto give rewards for discord boost
  daily:
    description: Created the code for RewardPro ( Causing crashes )
  prestige:
    aliases:
      - levelup
      - rankup
    description: Custom prestige System
  autoprestige:
    aliases:
      - autolevelup
      - autorankup
    description: Auto Prestige System
  autorebirth:
    description: Auto Rebirth System
  beaconpoints:
    description: Valuta utilizzata per gli enchant del Beacon pick
  anticheat:
    description: AlphaPrison Custom AntiCheat
  test:
    description: test command
  luckyblock:
    description: LuckyBlock System
  milestones:
    description: Custom MileStones System
  class:
    description: Custom Class System
  saveData:
    description: Global Manual Save Data Feature
  alignData:
    description: Global Alignment for Data
  voteshop:
    description: Custom Vote Shop
  enchanter:
    description: Free enchants for VIPs Feature
  quest:
    aliases:
      - quests
    description: Quest System
  fixdata:
    description: Fix Pickaxe Experience without database
  blockMilestone:
    description: Block Milestones for EzBlocks
  commandLog:
    description: Custom command log
  speedlevel:
    description: Custom speed effect system
  smarttips:
    description: Smart feature broadcaster system for managing and testing tips
  hastelevel:
    description: Custom haste effect system
  maxprestige:
    aliases:
      - prestigemax
      - rankupmax
      - maxrankup
    description: alias for /prestige max
  fixstuff:
    description: Command used for fixing stuff
  globalbooster:
    description: Command used to activate global money and token boosters
  minebomb:
    description: Mine Bomb commands
  refillableminebomb:
    aliases:
      - rmb
      - refillablebomb
    description: Refillable Mine Bomb commands
  settings:
    aliases:
      - setting
      - alphasettings
    description: Open Settings GUI
  blockbreakstats:
    aliases:
      - bbstats
      - threadstats
    description: Show block break threading statistics
  togglethreading:
    aliases:
      - togglebb
      - bbthread
    description: Toggle block break threading on/off
  asynckeys:
    description: Async Key Opening Admin Commands
  minebuddy:
    aliases:
      - mb
      - buddy
    description: Mine Buddy system commands
  recipes:
    aliases:
      - recipe
      - crafting
      - customrecipes
    description: View all available custom crafting recipes